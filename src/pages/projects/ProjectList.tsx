import React, { useEffect, useState } from 'react';
import { Table, Button, Input, Select, Card, Tag, Space, message } from 'antd';
import { SearchOutlined, PlusOutlined, EditOutlined, DeleteOutlined, BarChartOutlined, SyncOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../../store';
import { fetchProjectsStart, fetchProjectsSuccess, deleteProject, setCurrentProject } from '../../store/slices/projectsSlice';
import { PageHeader, EmptyState, LoadingSpinner, ClearDataButton, ConfirmDialog, SyncStatus } from '../../components/common';
import { formatDate, formatCurrency } from '../../utils';
import { Project, ProjectStatus } from '../../types';
import { CurrencyType } from '../../types/settings';
import { getProjectList, deleteProject as deleteProjectSync, syncProjectToServer, cleanupCorruptedLocalStorage } from '../../services/projectSyncService';

const { Option } = Select;

const ProjectList: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  // 添加安全检查
  let projects: Project[] = [];
  let isLoading = false;
  let currency = 'JPY';

  try {
    const projectsState = useAppSelector((state) => state.projects);
    const settingsState = useAppSelector((state) => state.settings);

    projects = projectsState?.projects || [];
    isLoading = projectsState?.isLoading || false;
    currency = settingsState?.currency || 'JPY';
  } catch (error) {
    console.error('获取Redux状态时出错:', error);
  }

  // 搜索和筛选状态
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState<ProjectStatus | 'all'>('all');

  // 删除确认对话框状态
  const [deleteConfirmVisible, setDeleteConfirmVisible] = useState(false);
  const [projectToDelete, setProjectToDelete] = useState<string | null>(null);

  // 获取项目列表
  useEffect(() => {
    console.log('项目列表页面加载，当前项目数量:', projects.length);

    // 首先清理损坏的本地存储数据
    cleanupCorruptedLocalStorage();

    fetchProjects();

    // 监听项目列表刷新事件
    const handleRefresh = () => {
      console.log('收到项目列表刷新事件，重新获取项目列表');
      fetchProjects();
    };

    window.addEventListener('project-list-refresh', handleRefresh);

    return () => {
      window.removeEventListener('project-list-refresh', handleRefresh);
    };
  }, []);

  // 获取项目列表
  const fetchProjects = async () => {
    try {
      console.log('开始获取项目列表...', new Date().toISOString());
      dispatch(fetchProjectsStart());

      // 使用同步服务获取项目列表
      console.log('调用getProjectList...');
      const response = await getProjectList();

      console.log('获取到项目数据条数:', response.items ? response.items.length : '未知');

      if (response.items && Array.isArray(response.items)) {
        console.log('项目数据是数组，长度:', response.items.length);

        // 获取本地存储的项目列表，合并服务器和本地数据
        const localProjects = [];
        const localKeys = Object.keys(localStorage).filter(key => key.startsWith('pv_project_'));

        for (const key of localKeys) {
          try {
            const localData = localStorage.getItem(key);
            if (localData) {
              // 尝试清理可能导致解析错误的特殊字符
              const cleanedData = localData.replace(/[\u0000-\u001F\u007F-\u009F]/g, '');
              const project = JSON.parse(cleanedData);

              // 验证项目数据的完整性
              if (project && project.id && typeof project.id === 'string') {
                // 检查是否在服务器列表中
                const existsInServer = response.items.some(serverProject => serverProject.id === project.id);
                if (!existsInServer) {
                  // 如果本地项目不在服务器列表中，添加到本地项目列表
                  localProjects.push(project);
                  console.log('发现本地独有项目:', project.name, project.id);
                }
              } else {
                console.warn('本地项目数据无效，缺少必要字段:', project);
                // 删除无效的本地存储项
                localStorage.removeItem(key);
                console.log('已删除无效的本地存储项:', key);
              }
            }
          } catch (parseError) {
            console.error('解析本地项目数据失败:', parseError, '键名:', key);
            // 删除损坏的本地存储项
            localStorage.removeItem(key);
            console.log('已删除损坏的本地存储项:', key);
          }
        }

        // 合并服务器和本地项目
        const allProjects = [...response.items, ...localProjects];
        console.log('合并后的项目总数:', allProjects.length);
        dispatch(fetchProjectsSuccess(allProjects));
      } else {
        console.error('项目数据不是数组:', response);
        dispatch(fetchProjectsSuccess([]));
      }
    } catch (error) {
      console.error('获取项目列表失败:', error);
      console.error('错误详情:', JSON.stringify(error, Object.getOwnPropertyNames(error)));

      // 如果服务器获取失败，尝试从本地存储获取
      console.log('服务器获取失败，尝试从本地存储获取项目列表');
      try {
        const localProjects = [];
        const localKeys = Object.keys(localStorage).filter(key => key.startsWith('pv_project_'));

        for (const key of localKeys) {
          try {
            const localData = localStorage.getItem(key);
            if (localData) {
              // 尝试清理可能导致解析错误的特殊字符
              const cleanedData = localData.replace(/[\u0000-\u001F\u007F-\u009F]/g, '');
              const project = JSON.parse(cleanedData);

              // 验证项目数据的完整性
              if (project && project.id && typeof project.id === 'string') {
                localProjects.push(project);
              } else {
                console.warn('本地项目数据无效，缺少必要字段:', project);
                // 删除无效的本地存储项
                localStorage.removeItem(key);
                console.log('已删除无效的本地存储项:', key);
              }
            }
          } catch (parseError) {
            console.error('解析本地项目数据失败:', parseError, '键名:', key);
            // 删除损坏的本地存储项
            localStorage.removeItem(key);
            console.log('已删除损坏的本地存储项:', key);
          }
        }

        console.log('从本地存储获取到项目数量:', localProjects.length);
        dispatch(fetchProjectsSuccess(localProjects));

        if (localProjects.length > 0) {
          message.warning('服务器连接失败，显示本地项目');
        } else {
          message.error(t('projects.fetchFailed'));
        }
      } catch (localError) {
        console.error('从本地存储获取项目失败:', localError);
        message.error(t('projects.fetchFailed'));
        dispatch(fetchProjectsSuccess([]));
      }
    }
  };

  // 处理新建项目
  const handleNewProject = () => {
    // 清理当前项目状态，确保新建项目时不会预填充数据
    dispatch(setCurrentProject(null));
    navigate('/projects/new');
  };

  // 处理编辑项目
  const handleEditProject = (id: string) => {
    // 先设置当前项目，然后导航到编辑项目页面
    const projectToEdit = projects.find(project => project.id === id);
    if (projectToEdit) {
      dispatch(setCurrentProject(projectToEdit));
      navigate(`/projects/edit/${id}`);
    } else {
      message.error(t('projects.notFound'));
    }
  };

  // 处理查看项目分析结果
  const handleViewAnalysis = (id: string) => {
    navigate(`/projects/analysis/${id}`);
  };

  // 处理删除项目
  const handleDeleteProject = (id: string) => {
    // 显示确认对话框
    setProjectToDelete(id);
    setDeleteConfirmVisible(true);
  };

  // 确认删除项目
  const confirmDeleteProject = async () => {
    if (projectToDelete) {
      try {
        // 使用同步服务删除项目
        await deleteProjectSync(projectToDelete);

        // 更新Redux状态
        dispatch(deleteProject(projectToDelete));

        message.success(t('projects.deleted'));
      } catch (error) {
        console.error('删除项目失败:', error);
        message.error(t('projects.deleteFailed'));
      } finally {
        setDeleteConfirmVisible(false);
        setProjectToDelete(null);
      }
    }
  };

  // 取消删除项目
  const cancelDeleteProject = () => {
    setDeleteConfirmVisible(false);
    setProjectToDelete(null);
  };

  // 同步项目到服务器
  const handleSyncProject = async (id: string) => {
    try {
      message.loading({ content: t('projects.syncing'), key: 'sync', duration: 0 });
      console.log(`开始同步项目 ${id} 到服务器...`);

      // 使用同步服务同步项目到服务器
      try {
        const syncedProject = await syncProjectToServer(id);
        console.log('同步结果:', syncedProject);

        // 更新Redux状态
        dispatch(fetchProjectsStart());
        await fetchProjects();

        message.success({ content: t('projects.syncSuccess'), key: 'sync' });
      } catch (syncError) {
        console.error('同步项目到服务器失败:', syncError);
        message.error({ content: t('projects.syncFailed'), key: 'sync' });

        // 刷新项目列表以显示最新状态
        dispatch(fetchProjectsStart());
        await fetchProjects();
      }
    } catch (error) {
      console.error('同步项目处理失败:', error);
      message.error({ content: t('projects.syncFailed'), key: 'sync' });
    }
  };

  // 过滤项目 - 添加安全检查
  const filteredProjects = (projects || []).filter((project) => {
    // 添加安全检查，确保项目数据完整
    if (!project || typeof project !== 'object') {
      console.warn('发现无效的项目数据:', project);
      return false;
    }

    // 确保必要字段存在
    const projectName = project.name || '';
    const projectLocation = project.location || '';
    const projectStatus = project.status || 'draft';

    try {
      const matchesSearch = projectName.toLowerCase().includes(searchText.toLowerCase()) ||
        projectLocation.toLowerCase().includes(searchText.toLowerCase());
      const matchesStatus = statusFilter === 'all' || projectStatus === statusFilter;
      return matchesSearch && matchesStatus;
    } catch (error) {
      console.error('过滤项目时出错:', error, project);
      return false;
    }
  });

  // 表格列定义
  const columns = [
    {
      title: t('projects.name'),
      dataIndex: 'name',
      key: 'name',
      render: (name: string) => name || '-',
      sorter: (a: Project, b: Project) => (a.name || '').localeCompare(b.name || ''),
    },
    {
      title: t('projects.location'),
      dataIndex: 'location',
      key: 'location',
      render: (location: string) => location || '-',
    },
    {
      title: t('projects.capacity'),
      dataIndex: 'capacity',
      key: 'capacity',
      render: (capacity: number) => `${capacity || 0} kW`,
      sorter: (a: Project, b: Project) => (a.capacity || 0) - (b.capacity || 0),
    },
    {
      title: t('projects.status'),
      dataIndex: 'status',
      key: 'status',
      render: (status: ProjectStatus) => {
        const safeStatus = status || 'draft';
        let color = 'default';
        if (safeStatus === 'completed') color = 'success';
        if (safeStatus === 'analyzing') color = 'processing';
        if (safeStatus === 'draft') color = 'warning';
        return <Tag color={color}>{t(`projects.${safeStatus}`)}</Tag>;
      },
    },
    {
      title: t('common.syncStatus'),
      dataIndex: 'syncStatus',
      key: 'syncStatus',
      render: (syncStatus: 'synced' | 'local-only' | 'server-only' | 'invalid' | undefined) => <SyncStatus status={syncStatus} />,
    },
    {
      title: t('projects.createdAt'),
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => date ? formatDate(date) : '-',
      sorter: (a: Project, b: Project) => {
        const aTime = a.createdAt ? new Date(a.createdAt).getTime() : 0;
        const bTime = b.createdAt ? new Date(b.createdAt).getTime() : 0;
        return aTime - bTime;
      },
    },
    {
      title: t('analysis.totalBenefit'),
      key: 'totalBenefit',
      render: (_: any, record: Project) => {
        // 添加更多的安全检查
        return (record.analysisResults && record.analysisResults.yearlyData &&
                typeof record.analysisResults.yearlyData.totalBenefit === 'number')
          ? formatCurrency(record.analysisResults.yearlyData.totalBenefit, currency as CurrencyType)
          : '-';
      },
      sorter: (a: Project, b: Project) => {
        // 添加更多的安全检查
        const aValue = (a.analysisResults && a.analysisResults.yearlyData &&
                       typeof a.analysisResults.yearlyData.totalBenefit === 'number')
          ? a.analysisResults.yearlyData.totalBenefit : 0;
        const bValue = (b.analysisResults && b.analysisResults.yearlyData &&
                       typeof b.analysisResults.yearlyData.totalBenefit === 'number')
          ? b.analysisResults.yearlyData.totalBenefit : 0;
        return aValue - bValue;
      },
    },
    {
      title: t('common.actions'),
      key: 'actions',
      render: (_: any, record: Project) => {
        // 添加安全检查
        if (!record || !record.id) {
          return null;
        }

        return (
          <Space>
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEditProject(record.id)}
            />
            {(record.status || 'draft') === 'completed' && (
              <Button
                type="text"
                icon={<BarChartOutlined />}
                onClick={() => handleViewAnalysis(record.id)}
              />
            )}
            {(record.syncStatus || 'local-only') === 'local-only' && (
              <Button
                type="text"
                icon={<SyncOutlined />}
                onClick={() => handleSyncProject(record.id)}
                title={t('projects.syncToServer')}
              />
            )}
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleDeleteProject(record.id)}
            />
          </Space>
        );
      },
    },
  ];

  if (isLoading) {
    return <LoadingSpinner fullScreen />;
  }

  // 调试信息已移除

  try {
    return (
      <div>
        <PageHeader
          title={t('projects.title')}
          extra={
            <Space>
              <ClearDataButton />
              <Button type="primary" icon={<PlusOutlined />} onClick={handleNewProject}>
                {t('projects.new')}
              </Button>
            </Space>
          }
        />

        <Card style={{ marginBottom: '16px' }}>
          <div style={{ display: 'flex', flexWrap: 'wrap', gap: '16px' }}>
            <Input
              placeholder={t('projects.search')}
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              style={{ width: '256px' }}
            />
            <Select
              placeholder={t('projects.filter')}
              value={statusFilter}
              onChange={(value) => setStatusFilter(value)}
              style={{ width: '160px' }}
            >
              <Option value="all">{t('common.all')}</Option>
              <Option value="draft">{t('projects.draft')}</Option>
              <Option value="analyzing">{t('projects.analyzing')}</Option>
              <Option value="completed">{t('projects.completed')}</Option>
            </Select>
          </div>
        </Card>

        {filteredProjects.length === 0 ? (
          <EmptyState
            title={t('common.noData')}
            description={t('projects.noProjects')}
            actionText={t('projects.new')}
            onAction={handleNewProject}
          />
        ) : (
          <Table
            dataSource={filteredProjects}
            columns={columns}
            rowKey={(record) => record.id || Math.random().toString()}
            pagination={{ pageSize: 10 }}
          />
        )}

        {/* 删除确认对话框 */}
        <ConfirmDialog
          visible={deleteConfirmVisible}
          title={t('projects.confirmDelete')}
          content={t('projects.deleteConfirmation')}
          onConfirm={confirmDeleteProject}
          onCancel={cancelDeleteProject}
        />
      </div>
    );
  } catch (error) {
    console.error('ProjectList 渲染错误:', error);
    return (
      <div style={{ padding: '20px', textAlign: 'center' }}>
        <h3>页面加载出错</h3>
        <p>请刷新页面重试</p>
        <Button type="primary" onClick={() => window.location.reload()}>
          刷新页面
        </Button>
      </div>
    );
  }
};

export default ProjectList;
