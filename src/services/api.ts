/**
 * API服务
 * 用于与后端API通信
 */
import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import { store } from '../store';

// 服务器控制台页面URL
const SERVER_CONSOLE_URL = '/restart.html';

// 检查是否已经打开了控制台页面
let consoleWindowOpened = false;

// 打开服务器控制台页面
const openServerConsole = () => {
  if (!consoleWindowOpened) {
    consoleWindowOpened = true;
    console.log('🚀 正在打开服务器控制台页面...');

    const consoleWindow = window.open(SERVER_CONSOLE_URL, 'serverConsole', 'width=600,height=700,scrollbars=yes,resizable=yes');

    // 监听窗口关闭事件，重置标志
    if (consoleWindow) {
      const checkClosed = setInterval(() => {
        if (consoleWindow.closed) {
          consoleWindowOpened = false;
          clearInterval(checkClosed);
          console.log('📱 服务器控制台页面已关闭');
        }
      }, 1000);
    }

    // 5分钟后重置标志，避免长时间无法再次打开
    setTimeout(() => {
      consoleWindowOpened = false;
    }, 5 * 60 * 1000);
  } else {
    console.log('📱 服务器控制台页面已经打开');
  }
};

// 检查是否为服务器连接错误
const isServerConnectionError = (error: any): boolean => {
  // 网络错误或服务器无响应
  if (error.code === 'ECONNREFUSED' || error.code === 'NETWORK_ERROR') {
    return true;
  }

  // 请求超时
  if (error.code === 'ECONNABORTED' && error.message.includes('timeout')) {
    return true;
  }

  // 没有收到响应（服务器可能已停止）
  if (error.request && !error.response) {
    return true;
  }

  // 5xx服务器错误
  if (error.response && error.response.status >= 500) {
    return true;
  }

  return false;
};

// 创建axios实例
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || (import.meta.env.DEV ? 'http://localhost:3001/api' : '/api'), // API基础URL
  timeout: 30000, // 请求超时时间
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 从Redux store获取token
    const { token } = store.getState().auth;

    // 如果有token，添加到请求头
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // 调试日志
    console.log(`🚀 发送${config.method?.toUpperCase()}请求到: ${config.baseURL}${config.url}`, config.params || config.data);
    return config;
  },
  (error) => {
    console.error('❌ 请求拦截器错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    // 调试日志
    console.log(`✅ 收到响应: ${response.config.url}`, response.status, response.data);
    return response;
  },
  (error) => {
    console.error(`❌ 响应错误: ${error.config?.url || '未知URL'}`, error);

    // 详细记录错误信息
    if (error.response) {
      // 服务器返回了错误状态码
      console.error('📡 服务器响应:', {
        status: error.response.status,
        headers: error.response.headers,
        data: error.response.data
      });

      const { status } = error.response;

      // 处理401未授权错误
      if (status === 401) {
        console.error('未授权，请重新登录');
      }

      // 处理403禁止访问错误
      if (status === 403) {
        console.error('禁止访问');
      }

      // 处理404未找到错误
      if (status === 404) {
        console.error('请求的资源不存在');
      }

      // 处理500服务器错误
      if (status >= 500) {
        console.error('服务器错误');
        // 5xx错误也应该打开控制台页面
        if (isServerConnectionError(error)) {
          console.warn('🚨 检测到服务器错误，正在打开服务器控制台页面...');
          openServerConsole();
        }
      }
    } else if (error.request) {
      // 请求已发送但没有收到响应
      console.error('📡 请求已发送但没有收到响应:', error.request);
      console.error('📡 请求配置:', error.config);
      console.error('网络错误，无法连接到服务器');

      // 检查是否为服务器连接错误，如果是则打开控制台页面
      if (isServerConnectionError(error)) {
        console.warn('🚨 检测到服务器连接错误，正在打开服务器控制台页面...');
        openServerConsole();
      }

      // 检查服务器连接
      console.log('🔍 正在检查服务器连接...');
      const healthUrl = import.meta.env.DEV ? 'http://localhost:3001/api/health' : '/api/health';
      fetch(healthUrl)
        .then(response => {
          console.log('✅ 服务器连接测试成功:', response.status);
        })
        .catch(err => {
          console.error('❌ 服务器连接测试失败:', err);
          console.log('💡 提示: 请确保服务器正在运行');
          // 如果健康检查也失败，再次确认打开控制台页面
          openServerConsole();
        });
    } else {
      // 设置请求时发生错误
      console.error('📡 请求设置错误:', error.message);
    }

    return Promise.reject(error);
  }
);

/**
 * GET请求
 * @param url 请求URL
 * @param params 请求参数
 * @param config 请求配置
 * @returns Promise
 */
export const get = <T>(
  url: string,
  params?: any,
  config?: AxiosRequestConfig
): Promise<AxiosResponse<T>> => {
  return api.get(url, { params, ...config });
};

/**
 * POST请求
 * @param url 请求URL
 * @param data 请求数据
 * @param config 请求配置
 * @returns Promise
 */
export const post = <T>(
  url: string,
  data?: any,
  config?: AxiosRequestConfig
): Promise<AxiosResponse<T>> => {
  return api.post(url, data, config);
};

/**
 * PUT请求
 * @param url 请求URL
 * @param data 请求数据
 * @param config 请求配置
 * @returns Promise
 */
export const put = <T>(
  url: string,
  data?: any,
  config?: AxiosRequestConfig
): Promise<AxiosResponse<T>> => {
  return api.put(url, data, config);
};

/**
 * DELETE请求
 * @param url 请求URL
 * @param config 请求配置
 * @returns Promise
 */
export const del = <T>(
  url: string,
  config?: AxiosRequestConfig
): Promise<AxiosResponse<T>> => {
  return api.delete(url, config);
};

/**
 * 上传文件
 * @param url 请求URL
 * @param file 文件
 * @param onProgress 进度回调
 * @returns Promise
 */
export const uploadFile = <T>(
  url: string,
  file: File,
  onProgress?: (progressEvent: any) => void
): Promise<AxiosResponse<T>> => {
  const formData = new FormData();
  formData.append('file', file);

  return api.post(url, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    onUploadProgress: onProgress,
  });
};
