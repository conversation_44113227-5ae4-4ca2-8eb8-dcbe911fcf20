{"id": "e4991f90-9378-4017-952d-74cb4c734f1e", "status": "completed", "createdAt": "2025-05-30T15:32:07.032Z", "updatedAt": "2025-05-30T15:32:07.032Z", "basicInfo": {"regionCode": "kanto", "prefectureCode": "saitama", "name": "高总家", "capacity": 18, "region": "关东地方", "prefecture": "埼玉县", "installationType": "rooftop", "projectType": "residential", "connectionType": "hybrid", "description": ""}, "pvModules": [{"id": "1748618493809", "equipmentId": "2247571d-9faf-48bf-9494-9b1189931e11", "name": "南向", "manufacturer": "晶科", "model": "JKM605N-78HL4-BDV", "quantity": 14, "power": 605, "efficiency": 21.6, "area": 2.79, "price": 12000, "angle": 30, "orientation": "south", "orientationAngle": 0}, {"id": "1748618687936", "equipmentId": "2247571d-9faf-48bf-9494-9b1189931e11", "name": "北向", "manufacturer": "晶科", "model": "JKM605N-78HL4-BDV", "quantity": 14, "power": 605, "efficiency": 21.6, "area": 2.79, "price": 12000, "angle": 30, "orientation": "north", "orientationAngle": 180}], "energyStorage": [{"id": "1748618541905", "name": "16度电储能包", "manufacturer": "星翼", "model": "XY-16.1H", "quantity": 1, "capacity": 16.1, "power": 8, "efficiency": 93, "cycles": 6000, "price": 200000}], "inverters": [{"id": "1748618553636", "name": "古瑞瓦特12KW逆变器", "manufacturer": "古瑞瓦特", "model": "SPE12000ES", "quantity": 1, "power": 12, "efficiency": 95.5, "price": 35000}], "otherInvestments": [{"id": "1748618584997", "name": "辅料", "category": "construction", "price": 10000}, {"id": "1748618607185", "name": "安装", "category": "construction", "price": 30000}], "name": "高总家", "location": "", "capacity": 18, "irradianceDataId": "dd7099a9-e9d3-48d0-abed-172f6af2aec9", "electricityPriceId": "a8b13f69-a657-4319-aed6-6080c6754836", "electricityUsage": {"type": "sameEveryday", "data": [{"hour": 0, "value": 1}, {"hour": 1, "value": 0.5}, {"hour": 2, "value": 0.5}, {"hour": 3, "value": 0.5}, {"hour": 4, "value": 1}, {"hour": 5, "value": 1.5}, {"hour": 6, "value": 1.5}, {"hour": 7, "value": 2}, {"hour": 8, "value": 2.5}, {"hour": 9, "value": 2.5}, {"hour": 10, "value": 3}, {"hour": 11, "value": 2.5}, {"hour": 12, "value": 3}, {"hour": 13, "value": 2.5}, {"hour": 14, "value": 2}, {"hour": 15, "value": 1}, {"hour": 16, "value": 1}, {"hour": 17, "value": 1}, {"hour": 18, "value": 2}, {"hour": 19, "value": 2.5}, {"hour": 20, "value": 3}, {"hour": 21, "value": 2.5}, {"hour": 22, "value": 2.5}, {"hour": 23, "value": 1.5}]}, "analysisResults": {"hourlyData": [], "dailyData": [{"month": 1, "day": 1, "pvGeneration": 14.694, "storageCharge": 0.078, "storageDischarge": 0.073, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 28.814, "gridExportIncome": 0, "pvBenefit": 631.567, "storageBenefit": 2.057, "totalBenefit": 633.623}, {"month": 1, "day": 2, "pvGeneration": 28.668, "storageCharge": 12.455, "storageDischarge": 11.583, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 16.618, "gridExportIncome": 0, "pvBenefit": 868.414, "storageBenefit": 325.144, "totalBenefit": 1193.558}, {"month": 1, "day": 3, "pvGeneration": 48.215, "storageCharge": 16.695, "storageDischarge": 15.528, "electricityConsumption": 43.5, "gridExport": 10.66, "gridImport": 8.341, "gridExportIncome": 159.9, "pvBenefit": 1285.511, "storageBenefit": 369.51, "totalBenefit": 1655.02}, {"month": 1, "day": 4, "pvGeneration": 42.491, "storageCharge": 16.694, "storageDischarge": 15.527, "electricityConsumption": 43.5, "gridExport": 5.32, "gridImport": 8.725, "gridExportIncome": 79.8, "pvBenefit": 1192.78, "storageBenefit": 369.306, "totalBenefit": 1562.086}, {"month": 1, "day": 5, "pvGeneration": 35.419, "storageCharge": 15.413, "storageDischarge": 14.334, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 10.294, "gridExportIncome": 0, "pvBenefit": 1079.534, "storageBenefit": 347.399, "totalBenefit": 1426.933}, {"month": 1, "day": 6, "pvGeneration": 19.187, "storageCharge": 7.646, "storageDischarge": 7.11, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 25.408, "gridExportIncome": 0, "pvBenefit": 617.463, "storageBenefit": 199.572, "totalBenefit": 817.035}, {"month": 1, "day": 7, "pvGeneration": 42.908, "storageCharge": 16.694, "storageDischarge": 15.527, "electricityConsumption": 43.5, "gridExport": 5.746, "gridImport": 8.731, "gridExportIncome": 86.19, "pvBenefit": 1199.274, "storageBenefit": 368.957, "totalBenefit": 1568.231}, {"month": 1, "day": 8, "pvGeneration": 26.512, "storageCharge": 7.292, "storageDischarge": 6.781, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 18.033, "gridExportIncome": 0, "pvBenefit": 929.605, "storageBenefit": 190.34, "totalBenefit": 1119.945}, {"month": 1, "day": 9, "pvGeneration": 23.312, "storageCharge": 8.77, "storageDischarge": 8.157, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 21.448, "gridExportIncome": 0, "pvBenefit": 761.292, "storageBenefit": 228.989, "totalBenefit": 990.281}, {"month": 1, "day": 10, "pvGeneration": 24.625, "storageCharge": 7.987, "storageDischarge": 7.428, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 20.023, "gridExportIncome": 0, "pvBenefit": 829.539, "storageBenefit": 208.513, "totalBenefit": 1038.051}, {"month": 1, "day": 11, "pvGeneration": 45.536, "storageCharge": 16.695, "storageDischarge": 15.527, "electricityConsumption": 43.5, "gridExport": 8.182, "gridImport": 8.538, "gridExportIncome": 122.73, "pvBenefit": 1242.225, "storageBenefit": 368.942, "totalBenefit": 1611.168}, {"month": 1, "day": 12, "pvGeneration": 41.374, "storageCharge": 16.697, "storageDischarge": 15.527, "electricityConsumption": 43.5, "gridExport": 4.471, "gridImport": 8.995, "gridExportIncome": 67.065, "pvBenefit": 1172.519, "storageBenefit": 367.866, "totalBenefit": 1540.385}, {"month": 1, "day": 13, "pvGeneration": 43.964, "storageCharge": 16.694, "storageDischarge": 15.527, "electricityConsumption": 43.5, "gridExport": 6.649, "gridImport": 8.581, "gridExportIncome": 99.735, "pvBenefit": 1218.284, "storageBenefit": 368.609, "totalBenefit": 1586.893}, {"month": 1, "day": 14, "pvGeneration": 36.565, "storageCharge": 15.964, "storageDischarge": 14.849, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 9.228, "gridExportIncome": 0, "pvBenefit": 1108.604, "storageBenefit": 355.226, "totalBenefit": 1463.83}, {"month": 1, "day": 15, "pvGeneration": 38.847, "storageCharge": 16.695, "storageDischarge": 15.527, "electricityConsumption": 43.5, "gridExport": 1.753, "gridImport": 8.804, "gridExportIncome": 26.295, "pvBenefit": 1138.825, "storageBenefit": 367.188, "totalBenefit": 1506.012}, {"month": 1, "day": 16, "pvGeneration": 49.244, "storageCharge": 16.696, "storageDischarge": 15.528, "electricityConsumption": 43.5, "gridExport": 11.716, "gridImport": 8.369, "gridExportIncome": 175.74, "pvBenefit": 1301.869, "storageBenefit": 368.089, "totalBenefit": 1669.957}, {"month": 1, "day": 17, "pvGeneration": 35.923, "storageCharge": 16.694, "storageDischarge": 15.527, "electricityConsumption": 43.5, "gridExport": 2.134, "gridImport": 12.107, "gridExportIncome": 32.01, "pvBenefit": 1025.695, "storageBenefit": 366.32, "totalBenefit": 1392.014}, {"month": 1, "day": 18, "pvGeneration": 50.079, "storageCharge": 16.694, "storageDischarge": 15.527, "electricityConsumption": 43.5, "gridExport": 12.506, "gridImport": 8.322, "gridExportIncome": 187.59, "pvBenefit": 1315.584, "storageBenefit": 367.726, "totalBenefit": 1683.31}, {"month": 1, "day": 19, "pvGeneration": 48.216, "storageCharge": 16.695, "storageDischarge": 15.527, "electricityConsumption": 43.5, "gridExport": 10.648, "gridImport": 8.33, "gridExportIncome": 159.72, "pvBenefit": 1287.318, "storageBenefit": 367.896, "totalBenefit": 1655.214}, {"month": 1, "day": 20, "pvGeneration": 44.818, "storageCharge": 16.694, "storageDischarge": 15.527, "electricityConsumption": 43.5, "gridExport": 7.395, "gridImport": 8.471, "gridExportIncome": 110.925, "pvBenefit": 1233.419, "storageBenefit": 368.26, "totalBenefit": 1601.679}, {"month": 1, "day": 21, "pvGeneration": 44.684, "storageCharge": 16.695, "storageDischarge": 15.527, "electricityConsumption": 43.5, "gridExport": 7.641, "gridImport": 8.853, "gridExportIncome": 114.615, "pvBenefit": 1225.116, "storageBenefit": 367.536, "totalBenefit": 1592.653}, {"month": 1, "day": 22, "pvGeneration": 22.293, "storageCharge": 3.606, "storageDischarge": 3.356, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 21.726, "gridExportIncome": 0, "pvBenefit": 856.878, "storageBenefit": 94.245, "totalBenefit": 951.123}, {"month": 1, "day": 23, "pvGeneration": 51.84, "storageCharge": 16.696, "storageDischarge": 15.527, "electricityConsumption": 43.5, "gridExport": 14.021, "gridImport": 8.078, "gridExportIncome": 210.315, "pvBenefit": 1346.987, "storageBenefit": 367.173, "totalBenefit": 1714.16}, {"month": 1, "day": 24, "pvGeneration": 52.899, "storageCharge": 16.696, "storageDischarge": 15.528, "electricityConsumption": 43.5, "gridExport": 15.014, "gridImport": 8.012, "gridExportIncome": 225.21, "pvBenefit": 1364.429, "storageBenefit": 366.857, "totalBenefit": 1731.286}, {"month": 1, "day": 25, "pvGeneration": 52.857, "storageCharge": 16.694, "storageDischarge": 15.528, "electricityConsumption": 43.5, "gridExport": 15.06, "gridImport": 8.099, "gridExportIncome": 225.9, "pvBenefit": 1362.207, "storageBenefit": 366.887, "totalBenefit": 1729.094}, {"month": 1, "day": 26, "pvGeneration": 50.326, "storageCharge": 16.695, "storageDischarge": 15.528, "electricityConsumption": 43.5, "gridExport": 12.644, "gridImport": 8.215, "gridExportIncome": 189.66, "pvBenefit": 1322.119, "storageBenefit": 366.872, "totalBenefit": 1688.991}, {"month": 1, "day": 27, "pvGeneration": 28.702, "storageCharge": 8.794, "storageDischarge": 8.179, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 16.059, "gridExportIncome": 0, "pvBenefit": 975.412, "storageBenefit": 229.602, "totalBenefit": 1205.014}, {"month": 1, "day": 28, "pvGeneration": 41.827, "storageCharge": 16.693, "storageDischarge": 15.528, "electricityConsumption": 43.5, "gridExport": 4.224, "gridImport": 8.297, "gridExportIncome": 63.36, "pvBenefit": 1191.949, "storageBenefit": 368.134, "totalBenefit": 1560.083}, {"month": 1, "day": 29, "pvGeneration": 46.397, "storageCharge": 16.697, "storageDischarge": 15.527, "electricityConsumption": 43.5, "gridExport": 8.804, "gridImport": 8.304, "gridExportIncome": 132.06, "pvBenefit": 1261.381, "storageBenefit": 366.983, "totalBenefit": 1628.364}, {"month": 1, "day": 30, "pvGeneration": 20.212, "storageCharge": 8.472, "storageDischarge": 7.879, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 24.505, "gridExportIncome": 0, "pvBenefit": 640.887, "storageBenefit": 221.172, "totalBenefit": 862.059}, {"month": 1, "day": 31, "pvGeneration": 28.175, "storageCharge": 8.361, "storageDischarge": 7.777, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 16.526, "gridExportIncome": 0, "pvBenefit": 965.013, "storageBenefit": 218.328, "totalBenefit": 1183.341}, {"month": 2, "day": 1, "pvGeneration": 43.008, "storageCharge": 16.696, "storageDischarge": 15.527, "electricityConsumption": 43.5, "gridExport": 5.122, "gridImport": 8.013, "gridExportIncome": 76.83, "pvBenefit": 1217.092, "storageBenefit": 365.766, "totalBenefit": 1582.859}, {"month": 2, "day": 2, "pvGeneration": 52.297, "storageCharge": 16.694, "storageDischarge": 15, "electricityConsumption": 43.5, "gridExport": 13.803, "gridImport": 7.888, "gridExportIncome": 207.045, "pvBenefit": 1374.145, "storageBenefit": 341.74, "totalBenefit": 1715.885}, {"month": 2, "day": 3, "pvGeneration": 30.95, "storageCharge": 10.632, "storageDischarge": 10.417, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 13.59, "gridExportIncome": 0, "pvBenefit": 1026.819, "storageBenefit": 280.056, "totalBenefit": 1306.876}, {"month": 2, "day": 4, "pvGeneration": 24.568, "storageCharge": 8.041, "storageDischarge": 7.478, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 20.085, "gridExportIncome": 0, "pvBenefit": 840.434, "storageBenefit": 209.913, "totalBenefit": 1050.346}, {"month": 2, "day": 5, "pvGeneration": 34.137, "storageCharge": 15.181, "storageDischarge": 14.119, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 11.542, "gridExportIncome": 0, "pvBenefit": 1022.213, "storageBenefit": 338.88, "totalBenefit": 1361.093}, {"month": 2, "day": 6, "pvGeneration": 37.377, "storageCharge": 15.938, "storageDischarge": 14.824, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 8.411, "gridExportIncome": 0, "pvBenefit": 1140.974, "storageBenefit": 349.966, "totalBenefit": 1490.94}, {"month": 2, "day": 7, "pvGeneration": 46.223, "storageCharge": 16.695, "storageDischarge": 15.527, "electricityConsumption": 43.5, "gridExport": 9.046, "gridImport": 8.718, "gridExportIncome": 135.69, "pvBenefit": 1251.195, "storageBenefit": 367.013, "totalBenefit": 1618.208}, {"month": 2, "day": 8, "pvGeneration": 41.629, "storageCharge": 16.694, "storageDischarge": 15, "electricityConsumption": 43.5, "gridExport": 5.677, "gridImport": 10.431, "gridExportIncome": 85.155, "pvBenefit": 1167.606, "storageBenefit": 341.74, "totalBenefit": 1509.346}, {"month": 2, "day": 9, "pvGeneration": 56.139, "storageCharge": 16.694, "storageDischarge": 15.528, "electricityConsumption": 43.5, "gridExport": 17.581, "gridImport": 7.34, "gridExportIncome": 263.715, "pvBenefit": 1432.891, "storageBenefit": 359.322, "totalBenefit": 1792.214}, {"month": 2, "day": 10, "pvGeneration": 52.589, "storageCharge": 16.694, "storageDischarge": 15.512, "electricityConsumption": 43.5, "gridExport": 14.027, "gridImport": 7.347, "gridExportIncome": 210.405, "pvBenefit": 1379.962, "storageBenefit": 358.615, "totalBenefit": 1738.577}, {"month": 2, "day": 11, "pvGeneration": 54.278, "storageCharge": 16.695, "storageDischarge": 15.544, "electricityConsumption": 43.5, "gridExport": 15.884, "gridImport": 7.487, "gridExportIncome": 238.26, "pvBenefit": 1402.012, "storageBenefit": 359.84, "totalBenefit": 1761.852}, {"month": 2, "day": 12, "pvGeneration": 56.078, "storageCharge": 16.696, "storageDischarge": 15.528, "electricityConsumption": 43.5, "gridExport": 17.651, "gridImport": 7.467, "gridExportIncome": 264.765, "pvBenefit": 1429.634, "storageBenefit": 359.292, "totalBenefit": 1788.926}, {"month": 2, "day": 13, "pvGeneration": 47.527, "storageCharge": 16.696, "storageDischarge": 15.559, "electricityConsumption": 43.5, "gridExport": 9.412, "gridImport": 7.75, "gridExportIncome": 141.18, "pvBenefit": 1295.321, "storageBenefit": 360.663, "totalBenefit": 1655.984}, {"month": 2, "day": 14, "pvGeneration": 51.499, "storageCharge": 16.695, "storageDischarge": 15.497, "electricityConsumption": 43.5, "gridExport": 13.032, "gridImport": 7.46, "gridExportIncome": 195.48, "pvBenefit": 1361.644, "storageBenefit": 358.275, "totalBenefit": 1719.919}, {"month": 2, "day": 15, "pvGeneration": 61.274, "storageCharge": 16.694, "storageDischarge": 15.512, "electricityConsumption": 43.5, "gridExport": 22.529, "gridImport": 7.168, "gridExportIncome": 337.935, "pvBenefit": 1513.513, "storageBenefit": 358.615, "totalBenefit": 1872.128}, {"month": 2, "day": 16, "pvGeneration": 59.735, "storageCharge": 16.694, "storageDischarge": 15.528, "electricityConsumption": 43.5, "gridExport": 20.976, "gridImport": 7.137, "gridExportIncome": 314.64, "pvBenefit": 1490.721, "storageBenefit": 359.148, "totalBenefit": 1849.869}, {"month": 2, "day": 17, "pvGeneration": 57.511, "storageCharge": 16.695, "storageDischarge": 15.512, "electricityConsumption": 43.5, "gridExport": 18.893, "gridImport": 7.291, "gridExportIncome": 283.395, "pvBenefit": 1454.973, "storageBenefit": 358.426, "totalBenefit": 1813.399}, {"month": 2, "day": 18, "pvGeneration": 62.434, "storageCharge": 16.696, "storageDischarge": 15.543, "electricityConsumption": 43.5, "gridExport": 23.677, "gridImport": 7.123, "gridExportIncome": 355.155, "pvBenefit": 1531.206, "storageBenefit": 359.618, "totalBenefit": 1890.823}, {"month": 2, "day": 19, "pvGeneration": 47.268, "storageCharge": 16.695, "storageDischarge": 15.528, "electricityConsumption": 43.5, "gridExport": 8.769, "gridImport": 7.394, "gridExportIncome": 131.535, "pvBenefit": 1299.012, "storageBenefit": 359.133, "totalBenefit": 1658.145}, {"month": 2, "day": 20, "pvGeneration": 39.035, "storageCharge": 16.694, "storageDischarge": 15.528, "electricityConsumption": 43.5, "gridExport": 1.083, "gridImport": 7.945, "gridExportIncome": 16.245, "pvBenefit": 1165.416, "storageBenefit": 359.148, "totalBenefit": 1524.564}, {"month": 2, "day": 21, "pvGeneration": 46.802, "storageCharge": 16.695, "storageDischarge": 15.528, "electricityConsumption": 43.5, "gridExport": 8.526, "gridImport": 7.621, "gridExportIncome": 127.89, "pvBenefit": 1287.868, "storageBenefit": 359.133, "totalBenefit": 1647.001}, {"month": 2, "day": 22, "pvGeneration": 59.811, "storageCharge": 16.695, "storageDischarge": 15.512, "electricityConsumption": 43.5, "gridExport": 21.023, "gridImport": 7.119, "gridExportIncome": 315.345, "pvBenefit": 1492.621, "storageBenefit": 358.426, "totalBenefit": 1851.046}, {"month": 2, "day": 23, "pvGeneration": 48.517, "storageCharge": 16.694, "storageDischarge": 16.087, "electricityConsumption": 43.5, "gridExport": 10.552, "gridImport": 7.413, "gridExportIncome": 158.28, "pvBenefit": 1299.324, "storageBenefit": 386.385, "totalBenefit": 1685.709}, {"month": 2, "day": 24, "pvGeneration": 57.692, "storageCharge": 16.695, "storageDischarge": 14.952, "electricityConsumption": 43.5, "gridExport": 18.925, "gridImport": 7.656, "gridExportIncome": 283.875, "pvBenefit": 1460.607, "storageBenefit": 339.603, "totalBenefit": 1800.211}, {"month": 2, "day": 25, "pvGeneration": 54.565, "storageCharge": 16.696, "storageDischarge": 15.495, "electricityConsumption": 43.5, "gridExport": 15.694, "gridImport": 7.054, "gridExportIncome": 235.41, "pvBenefit": 1415.918, "storageBenefit": 357.311, "totalBenefit": 1773.228}, {"month": 2, "day": 26, "pvGeneration": 49.917, "storageCharge": 16.697, "storageDischarge": 15.464, "electricityConsumption": 43.5, "gridExport": 11.637, "gridImport": 7.679, "gridExportIncome": 174.555, "pvBenefit": 1336.084, "storageBenefit": 355.566, "totalBenefit": 1691.649}, {"month": 2, "day": 27, "pvGeneration": 15.422, "storageCharge": 0.305, "storageDischarge": 0.956, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 27.504, "gridExportIncome": 0, "pvBenefit": 653.402, "storageBenefit": 30.355, "totalBenefit": 683.758}, {"month": 2, "day": 28, "pvGeneration": 62.138, "storageCharge": 16.694, "storageDischarge": 14.984, "electricityConsumption": 43.5, "gridExport": 23.282, "gridImport": 7.541, "gridExportIncome": 349.23, "pvBenefit": 1528.559, "storageBenefit": 341.033, "totalBenefit": 1869.592}, {"month": 3, "day": 1, "pvGeneration": 59.259, "storageCharge": 16.695, "storageDischarge": 15.48, "electricityConsumption": 43.5, "gridExport": 20.337, "gridImport": 7.02, "gridExportIncome": 305.055, "pvBenefit": 1487.068, "storageBenefit": 357.011, "totalBenefit": 1844.08}, {"month": 3, "day": 2, "pvGeneration": 49.283, "storageCharge": 16.696, "storageDischarge": 16.12, "electricityConsumption": 43.5, "gridExport": 12.267, "gridImport": 8.336, "gridExportIncome": 184.005, "pvBenefit": 1282.841, "storageBenefit": 398.016, "totalBenefit": 1680.857}, {"month": 3, "day": 3, "pvGeneration": 61.495, "storageCharge": 16.695, "storageDischarge": 14.871, "electricityConsumption": 43.5, "gridExport": 22.525, "gridImport": 7.532, "gridExportIncome": 337.875, "pvBenefit": 1522.184, "storageBenefit": 336.023, "totalBenefit": 1858.208}, {"month": 3, "day": 4, "pvGeneration": 27.478, "storageCharge": 6.676, "storageDischarge": 6.865, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 16.375, "gridExportIncome": 0, "pvBenefit": 981.656, "storageBenefit": 196.132, "totalBenefit": 1177.788}, {"month": 3, "day": 5, "pvGeneration": 39.982, "storageCharge": 16.695, "storageDischarge": 14.919, "electricityConsumption": 43.5, "gridExport": 2.257, "gridImport": 8.733, "gridExportIncome": 33.855, "pvBenefit": 1176.164, "storageBenefit": 338.145, "totalBenefit": 1514.309}, {"month": 3, "day": 6, "pvGeneration": 38.695, "storageCharge": 16.695, "storageDischarge": 16.136, "electricityConsumption": 43.5, "gridExport": 4.424, "gridImport": 11.065, "gridExportIncome": 66.36, "pvBenefit": 1063.237, "storageBenefit": 409.104, "totalBenefit": 1472.34}, {"month": 3, "day": 7, "pvGeneration": 66.521, "storageCharge": 16.695, "storageDischarge": 14.855, "electricityConsumption": 43.5, "gridExport": 27.407, "gridImport": 7.402, "gridExportIncome": 411.105, "pvBenefit": 1600.442, "storageBenefit": 335.316, "totalBenefit": 1935.758}, {"month": 3, "day": 8, "pvGeneration": 61.214, "storageCharge": 16.693, "storageDischarge": 15.495, "electricityConsumption": 43.5, "gridExport": 21.847, "gridImport": 6.56, "gridExportIncome": 327.705, "pvBenefit": 1525.816, "storageBenefit": 356.309, "totalBenefit": 1882.125}, {"month": 3, "day": 9, "pvGeneration": 66.668, "storageCharge": 16.694, "storageDischarge": 15.657, "electricityConsumption": 43.5, "gridExport": 27.638, "gridImport": 6.744, "gridExportIncome": 414.57, "pvBenefit": 1600.079, "storageBenefit": 363.095, "totalBenefit": 1963.174}, {"month": 3, "day": 10, "pvGeneration": 71.967, "storageCharge": 16.694, "storageDischarge": 15.447, "electricityConsumption": 43.5, "gridExport": 32.791, "gridImport": 6.792, "gridExportIncome": 491.865, "pvBenefit": 1683.071, "storageBenefit": 355.219, "totalBenefit": 2038.29}, {"month": 3, "day": 11, "pvGeneration": 76.639, "storageCharge": 16.695, "storageDischarge": 15.528, "electricityConsumption": 43.5, "gridExport": 37.447, "gridImport": 6.701, "gridExportIncome": 561.705, "pvBenefit": 1753.444, "storageBenefit": 357.901, "totalBenefit": 2111.345}, {"month": 3, "day": 12, "pvGeneration": 66.026, "storageCharge": 16.695, "storageDischarge": 15.609, "electricityConsumption": 43.5, "gridExport": 26.832, "gridImport": 6.627, "gridExportIncome": 402.48, "pvBenefit": 1593.413, "storageBenefit": 361.482, "totalBenefit": 1954.895}, {"month": 3, "day": 13, "pvGeneration": 30.854, "storageCharge": 9.18, "storageDischarge": 9.114, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 13.432, "gridExportIncome": 0, "pvBenefit": 1049.824, "storageBenefit": 258.806, "totalBenefit": 1308.63}, {"month": 3, "day": 14, "pvGeneration": 29.051, "storageCharge": 8.651, "storageDischarge": 8.044, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 15.691, "gridExportIncome": 0, "pvBenefit": 995.584, "storageBenefit": 225.78, "totalBenefit": 1221.364}, {"month": 3, "day": 15, "pvGeneration": 28.184, "storageCharge": 8.084, "storageDischarge": 7.516, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 16.472, "gridExportIncome": 0, "pvBenefit": 973.049, "storageBenefit": 210.947, "totalBenefit": 1183.996}, {"month": 3, "day": 16, "pvGeneration": 32.07, "storageCharge": 10.281, "storageDischarge": 9.56, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 12.908, "gridExportIncome": 0, "pvBenefit": 1069.103, "storageBenefit": 258.2, "totalBenefit": 1327.303}, {"month": 3, "day": 17, "pvGeneration": 19.186, "storageCharge": 1.02, "storageDischarge": 0.949, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 24.459, "gridExportIncome": 0, "pvBenefit": 789.904, "storageBenefit": 26.646, "totalBenefit": 816.55}, {"month": 3, "day": 18, "pvGeneration": 60.893, "storageCharge": 16.696, "storageDischarge": 14.742, "electricityConsumption": 43.5, "gridExport": 21.115, "gridImport": 6.843, "gridExportIncome": 316.725, "pvBenefit": 1529.376, "storageBenefit": 330.306, "totalBenefit": 1859.682}, {"month": 3, "day": 19, "pvGeneration": 63.758, "storageCharge": 16.695, "storageDischarge": 16.313, "electricityConsumption": 43.5, "gridExport": 24.26, "gridImport": 5.674, "gridExportIncome": 363.9, "pvBenefit": 1558.576, "storageBenefit": 391.258, "totalBenefit": 1949.834}, {"month": 3, "day": 20, "pvGeneration": 73.514, "storageCharge": 16.696, "storageDischarge": 14.742, "electricityConsumption": 43.5, "gridExport": 33.471, "gridImport": 6.574, "gridExportIncome": 502.065, "pvBenefit": 1723.596, "storageBenefit": 330.306, "totalBenefit": 2053.902}, {"month": 3, "day": 21, "pvGeneration": 71.334, "storageCharge": 16.695, "storageDischarge": 15.672, "electricityConsumption": 43.5, "gridExport": 31.52, "gridImport": 5.946, "gridExportIncome": 472.8, "pvBenefit": 1685.095, "storageBenefit": 362.871, "totalBenefit": 2047.966}, {"month": 3, "day": 22, "pvGeneration": 39.518, "storageCharge": 16.651, "storageDischarge": 15.592, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 6.273, "gridExportIncome": 0, "pvBenefit": 1202.528, "storageBenefit": 361.575, "totalBenefit": 1564.103}, {"month": 3, "day": 23, "pvGeneration": 14.712, "storageCharge": 0, "storageDischarge": 0.534, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 28.296, "gridExportIncome": 0, "pvBenefit": 623.565, "storageBenefit": 17.782, "totalBenefit": 641.348}, {"month": 3, "day": 24, "pvGeneration": 66.756, "storageCharge": 16.695, "storageDischarge": 14.791, "electricityConsumption": 43.5, "gridExport": 26.645, "gridImport": 6.464, "gridExportIncome": 399.675, "pvBenefit": 1622.892, "storageBenefit": 332.487, "totalBenefit": 1955.379}, {"month": 3, "day": 25, "pvGeneration": 57.378, "storageCharge": 16.695, "storageDischarge": 15.511, "electricityConsumption": 43.5, "gridExport": 16.926, "gridImport": 5.458, "gridExportIncome": 253.89, "pvBenefit": 1488.655, "storageBenefit": 356.278, "totalBenefit": 1844.933}, {"month": 3, "day": 26, "pvGeneration": 73.763, "storageCharge": 16.695, "storageDischarge": 15.463, "electricityConsumption": 43.5, "gridExport": 33.277, "gridImport": 5.47, "gridExportIncome": 499.155, "pvBenefit": 1735.713, "storageBenefit": 353.982, "totalBenefit": 2089.695}, {"month": 3, "day": 27, "pvGeneration": 48.766, "storageCharge": 16.695, "storageDischarge": 15.754, "electricityConsumption": 43.5, "gridExport": 9.004, "gridImport": 5.925, "gridExportIncome": 135.06, "pvBenefit": 1345.082, "storageBenefit": 366.136, "totalBenefit": 1711.218}, {"month": 3, "day": 28, "pvGeneration": 80.203, "storageCharge": 16.695, "storageDischarge": 15.205, "electricityConsumption": 43.5, "gridExport": 39.928, "gridImport": 5.918, "gridExportIncome": 598.92, "pvBenefit": 1829.498, "storageBenefit": 344.333, "totalBenefit": 2173.831}, {"month": 3, "day": 29, "pvGeneration": 66.04, "storageCharge": 16.693, "storageDischarge": 15.785, "electricityConsumption": 43.5, "gridExport": 25.683, "gridImport": 5.303, "gridExportIncome": 385.245, "pvBenefit": 1615.723, "storageBenefit": 366.489, "totalBenefit": 1982.213}, {"month": 3, "day": 30, "pvGeneration": 76.83, "storageCharge": 16.694, "storageDischarge": 15.19, "electricityConsumption": 43.5, "gridExport": 36.595, "gridImport": 5.976, "gridExportIncome": 548.925, "pvBenefit": 1779.028, "storageBenefit": 342.977, "totalBenefit": 2122.005}, {"month": 3, "day": 31, "pvGeneration": 74.549, "storageCharge": 16.694, "storageDischarge": 15.479, "electricityConsumption": 43.5, "gridExport": 33.807, "gridImport": 5.197, "gridExportIncome": 507.105, "pvBenefit": 1754.688, "storageBenefit": 352.066, "totalBenefit": 2106.754}, {"month": 4, "day": 1, "pvGeneration": 81.092, "storageCharge": 16.695, "storageDischarge": 15.672, "electricityConsumption": 43.5, "gridExport": 40.495, "gridImport": 5.165, "gridExportIncome": 607.425, "pvBenefit": 1848.57, "storageBenefit": 360.059, "totalBenefit": 2208.628}, {"month": 4, "day": 2, "pvGeneration": 84.99, "storageCharge": 16.694, "storageDischarge": 15.511, "electricityConsumption": 43.5, "gridExport": 44.361, "gridImport": 5.281, "gridExportIncome": 665.415, "pvBenefit": 1907.8, "storageBenefit": 354.538, "totalBenefit": 2262.338}, {"month": 4, "day": 3, "pvGeneration": 52.064, "storageCharge": 16.695, "storageDischarge": 15.64, "electricityConsumption": 43.5, "gridExport": 12.014, "gridImport": 5.743, "gridExportIncome": 180.21, "pvBenefit": 1402.075, "storageBenefit": 360.05, "totalBenefit": 1762.125}, {"month": 4, "day": 4, "pvGeneration": 75.685, "storageCharge": 16.694, "storageDischarge": 15.318, "electricityConsumption": 43.5, "gridExport": 34.928, "gridImport": 5.33, "gridExportIncome": 523.92, "pvBenefit": 1771.672, "storageBenefit": 347.065, "totalBenefit": 2118.737}, {"month": 4, "day": 5, "pvGeneration": 58.506, "storageCharge": 16.696, "storageDischarge": 15.37, "electricityConsumption": 43.5, "gridExport": 19.541, "gridImport": 7.078, "gridExportIncome": 293.115, "pvBenefit": 1482.879, "storageBenefit": 347.044, "totalBenefit": 1829.923}, {"month": 4, "day": 6, "pvGeneration": 87.804, "storageCharge": 16.695, "storageDischarge": 15.293, "electricityConsumption": 43.5, "gridExport": 46.666, "gridImport": 4.978, "gridExportIncome": 699.99, "pvBenefit": 1964.64, "storageBenefit": 341.934, "totalBenefit": 2306.573}, {"month": 4, "day": 7, "pvGeneration": 89.717, "storageCharge": 16.695, "storageDischarge": 15.471, "electricityConsumption": 43.5, "gridExport": 48.495, "gridImport": 4.723, "gridExportIncome": 727.425, "pvBenefit": 1995.555, "storageBenefit": 347.24, "totalBenefit": 2342.795}, {"month": 4, "day": 8, "pvGeneration": 37.755, "storageCharge": 14.545, "storageDischarge": 14.991, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 6.487, "gridExportIncome": 0, "pvBenefit": 1184.731, "storageBenefit": 370.689, "totalBenefit": 1555.42}, {"month": 4, "day": 9, "pvGeneration": 48.509, "storageCharge": 16.694, "storageDischarge": 14.903, "electricityConsumption": 43.5, "gridExport": 8.334, "gridImport": 6.29, "gridExportIncome": 125.01, "pvBenefit": 1349.189, "storageBenefit": 337.453, "totalBenefit": 1686.641}, {"month": 4, "day": 10, "pvGeneration": 49.569, "storageCharge": 16.694, "storageDischarge": 15.366, "electricityConsumption": 43.5, "gridExport": 11.875, "gridImport": 8.351, "gridExportIncome": 178.125, "pvBenefit": 1321.361, "storageBenefit": 351.116, "totalBenefit": 1672.476}, {"month": 4, "day": 11, "pvGeneration": 37.163, "storageCharge": 13.873, "storageDischarge": 13.69, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 7.606, "gridExportIncome": 0, "pvBenefit": 1179.99, "storageBenefit": 334.808, "totalBenefit": 1514.797}, {"month": 4, "day": 12, "pvGeneration": 83.434, "storageCharge": 16.695, "storageDischarge": 14, "electricityConsumption": 43.5, "gridExport": 42.303, "gridImport": 6.173, "gridExportIncome": 634.545, "pvBenefit": 1900.302, "storageBenefit": 297.525, "totalBenefit": 2197.827}, {"month": 4, "day": 13, "pvGeneration": 77.903, "storageCharge": 16.694, "storageDischarge": 16.27, "electricityConsumption": 43.5, "gridExport": 37.305, "gridImport": 4.615, "gridExportIncome": 559.575, "pvBenefit": 1799.55, "storageBenefit": 381.219, "totalBenefit": 2180.769}, {"month": 4, "day": 14, "pvGeneration": 68.113, "storageCharge": 16.695, "storageDischarge": 15.35, "electricityConsumption": 43.5, "gridExport": 32.486, "gridImport": 10.433, "gridExportIncome": 487.29, "pvBenefit": 1559.053, "storageBenefit": 348.638, "totalBenefit": 1907.692}, {"month": 4, "day": 15, "pvGeneration": 28.655, "storageCharge": 6.694, "storageDischarge": 7.189, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 14.917, "gridExportIncome": 0, "pvBenefit": 1024.049, "storageBenefit": 206.858, "totalBenefit": 1230.907}, {"month": 4, "day": 16, "pvGeneration": 84.138, "storageCharge": 16.695, "storageDischarge": 14.041, "electricityConsumption": 43.5, "gridExport": 42.759, "gridImport": 5.885, "gridExportIncome": 641.385, "pvBenefit": 1915.009, "storageBenefit": 299.337, "totalBenefit": 2214.346}, {"month": 4, "day": 17, "pvGeneration": 90.657, "storageCharge": 16.695, "storageDischarge": 15.487, "electricityConsumption": 43.5, "gridExport": 49.237, "gridImport": 4.512, "gridExportIncome": 738.555, "pvBenefit": 2013.991, "storageBenefit": 347.042, "totalBenefit": 2361.033}, {"month": 4, "day": 18, "pvGeneration": 93.191, "storageCharge": 16.694, "storageDischarge": 15.528, "electricityConsumption": 43.5, "gridExport": 51.771, "gridImport": 4.475, "gridExportIncome": 776.565, "pvBenefit": 2052.001, "storageBenefit": 348.422, "totalBenefit": 2400.423}, {"month": 4, "day": 19, "pvGeneration": 25.486, "storageCharge": 2.547, "storageDischarge": 3.895, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 16.969, "gridExportIncome": 0, "pvBenefit": 994.753, "storageBenefit": 117.299, "totalBenefit": 1112.052}, {"month": 4, "day": 20, "pvGeneration": 47.4, "storageCharge": 16.694, "storageDischarge": 13.984, "electricityConsumption": 43.5, "gridExport": 6.739, "gridImport": 6.656, "gridExportIncome": 101.085, "pvBenefit": 1351.402, "storageBenefit": 296.833, "totalBenefit": 1648.235}, {"month": 4, "day": 21, "pvGeneration": 52.694, "storageCharge": 16.694, "storageDischarge": 15.528, "electricityConsumption": 43.5, "gridExport": 11.275, "gridImport": 4.476, "gridExportIncome": 169.125, "pvBenefit": 1444.72, "storageBenefit": 348.248, "totalBenefit": 1792.968}, {"month": 4, "day": 22, "pvGeneration": 34.527, "storageCharge": 10.791, "storageDischarge": 11.579, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 9.096, "gridExportIncome": 0, "pvBenefit": 1147.246, "storageBenefit": 312.158, "totalBenefit": 1459.404}, {"month": 4, "day": 23, "pvGeneration": 81.822, "storageCharge": 16.694, "storageDischarge": 13.984, "electricityConsumption": 43.5, "gridExport": 40.309, "gridImport": 5.801, "gridExportIncome": 604.635, "pvBenefit": 1883.397, "storageBenefit": 296.833, "totalBenefit": 2180.23}, {"month": 4, "day": 24, "pvGeneration": 85.535, "storageCharge": 16.694, "storageDischarge": 15.544, "electricityConsumption": 43.5, "gridExport": 44.016, "gridImport": 4.364, "gridExportIncome": 660.24, "pvBenefit": 1938.918, "storageBenefit": 348.955, "totalBenefit": 2287.873}, {"month": 4, "day": 25, "pvGeneration": 92.861, "storageCharge": 16.695, "storageDischarge": 15.512, "electricityConsumption": 43.5, "gridExport": 51.165, "gridImport": 4.217, "gridExportIncome": 767.475, "pvBenefit": 2052.239, "storageBenefit": 347.7, "totalBenefit": 2399.94}, {"month": 4, "day": 26, "pvGeneration": 94.831, "storageCharge": 16.695, "storageDischarge": 15.528, "electricityConsumption": 43.5, "gridExport": 53.203, "gridImport": 4.266, "gridExportIncome": 798.045, "pvBenefit": 2080.582, "storageBenefit": 348.233, "totalBenefit": 2428.815}, {"month": 4, "day": 27, "pvGeneration": 89.875, "storageCharge": 16.695, "storageDischarge": 15.544, "electricityConsumption": 43.5, "gridExport": 47.845, "gridImport": 3.849, "gridExportIncome": 717.675, "pvBenefit": 2013.442, "storageBenefit": 348.94, "totalBenefit": 2362.382}, {"month": 4, "day": 28, "pvGeneration": 26.737, "storageCharge": 4.42, "storageDischarge": 5.638, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 15.99, "gridExportIncome": 0, "pvBenefit": 1004.395, "storageBenefit": 166.244, "totalBenefit": 1170.64}, {"month": 4, "day": 29, "pvGeneration": 51.985, "storageCharge": 16.695, "storageDischarge": 14.388, "electricityConsumption": 43.5, "gridExport": 10.645, "gridImport": 5.607, "gridExportIncome": 159.675, "pvBenefit": 1428.181, "storageBenefit": 314.675, "totalBenefit": 1742.856}, {"month": 4, "day": 30, "pvGeneration": 47.018, "storageCharge": 16.695, "storageDischarge": 15.205, "electricityConsumption": 43.5, "gridExport": 8.453, "gridImport": 7.632, "gridExportIncome": 126.795, "pvBenefit": 1306.385, "storageBenefit": 338.36, "totalBenefit": 1644.745}, {"month": 5, "day": 1, "pvGeneration": 68.308, "storageCharge": 16.695, "storageDischarge": 15.447, "electricityConsumption": 43.5, "gridExport": 26.106, "gridImport": 3.772, "gridExportIncome": 391.59, "pvBenefit": 1693.168, "storageBenefit": 345.536, "totalBenefit": 2038.703}, {"month": 5, "day": 2, "pvGeneration": 85.513, "storageCharge": 16.694, "storageDischarge": 15.512, "electricityConsumption": 43.5, "gridExport": 43.478, "gridImport": 3.873, "gridExportIncome": 652.17, "pvBenefit": 1948.471, "storageBenefit": 347.541, "totalBenefit": 2296.011}, {"month": 5, "day": 3, "pvGeneration": 73.274, "storageCharge": 16.696, "storageDischarge": 15.545, "electricityConsumption": 43.5, "gridExport": 31.277, "gridImport": 3.887, "gridExportIncome": 469.155, "pvBenefit": 1763.924, "storageBenefit": 348.784, "totalBenefit": 2112.709}, {"month": 5, "day": 4, "pvGeneration": 79.004, "storageCharge": 16.694, "storageDischarge": 15.463, "electricityConsumption": 43.5, "gridExport": 36.798, "gridImport": 3.748, "gridExportIncome": 551.97, "pvBenefit": 1854.452, "storageBenefit": 345.375, "totalBenefit": 2199.827}, {"month": 5, "day": 5, "pvGeneration": 50.251, "storageCharge": 16.696, "storageDischarge": 15.981, "electricityConsumption": 43.5, "gridExport": 8.863, "gridImport": 4.093, "gridExportIncome": 132.945, "pvBenefit": 1403.224, "storageBenefit": 367.532, "totalBenefit": 1770.756}, {"month": 5, "day": 6, "pvGeneration": 58.745, "storageCharge": 16.695, "storageDischarge": 15.173, "electricityConsumption": 43.5, "gridExport": 16.817, "gridImport": 4.29, "gridExportIncome": 252.255, "pvBenefit": 1544.466, "storageBenefit": 336.771, "totalBenefit": 1881.237}, {"month": 5, "day": 7, "pvGeneration": 72.868, "storageCharge": 16.695, "storageDischarge": 15.496, "electricityConsumption": 43.5, "gridExport": 30.858, "gridImport": 3.917, "gridExportIncome": 462.87, "pvBenefit": 1758.091, "storageBenefit": 347.167, "totalBenefit": 2105.258}, {"month": 5, "day": 8, "pvGeneration": 87.193, "storageCharge": 16.694, "storageDischarge": 15.528, "electricityConsumption": 43.5, "gridExport": 44.881, "gridImport": 3.587, "gridExportIncome": 673.215, "pvBenefit": 1978.456, "storageBenefit": 348.248, "totalBenefit": 2326.704}, {"month": 5, "day": 9, "pvGeneration": 52.096, "storageCharge": 16.695, "storageDischarge": 15.722, "electricityConsumption": 43.5, "gridExport": 10.405, "gridImport": 4.023, "gridExportIncome": 156.075, "pvBenefit": 1438.613, "storageBenefit": 356.808, "totalBenefit": 1795.421}, {"month": 5, "day": 10, "pvGeneration": 39.603, "storageCharge": 15.06, "storageDischarge": 15.357, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 4.813, "gridExportIncome": 0, "pvBenefit": 1240.832, "storageBenefit": 371.293, "totalBenefit": 1612.125}, {"month": 5, "day": 11, "pvGeneration": 60.569, "storageCharge": 16.695, "storageDischarge": 14.001, "electricityConsumption": 43.5, "gridExport": 18.563, "gridImport": 5.296, "gridExportIncome": 278.445, "pvBenefit": 1573.336, "storageBenefit": 297.569, "totalBenefit": 1870.905}, {"month": 5, "day": 12, "pvGeneration": 31.604, "storageCharge": 7.038, "storageDischarge": 8.077, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 11.5, "gridExportIncome": 0, "pvBenefit": 1121.002, "storageBenefit": 234.789, "totalBenefit": 1355.791}, {"month": 5, "day": 13, "pvGeneration": 90.604, "storageCharge": 16.695, "storageDischarge": 13.984, "electricityConsumption": 43.5, "gridExport": 48.433, "gridImport": 5.149, "gridExportIncome": 726.495, "pvBenefit": 2027.059, "storageBenefit": 296.818, "totalBenefit": 2323.876}, {"month": 5, "day": 14, "pvGeneration": 100.874, "storageCharge": 16.694, "storageDischarge": 15.463, "electricityConsumption": 43.5, "gridExport": 58.124, "gridImport": 3.204, "gridExportIncome": 871.86, "pvBenefit": 2192.457, "storageBenefit": 345.375, "totalBenefit": 2537.832}, {"month": 5, "day": 15, "pvGeneration": 97.664, "storageCharge": 16.696, "storageDischarge": 15.496, "electricityConsumption": 43.5, "gridExport": 55.201, "gridImport": 3.468, "gridExportIncome": 828.015, "pvBenefit": 2139.275, "storageBenefit": 346.095, "totalBenefit": 2485.37}, {"month": 5, "day": 16, "pvGeneration": 84.247, "storageCharge": 16.696, "storageDischarge": 15.56, "electricityConsumption": 43.5, "gridExport": 41.821, "gridImport": 3.439, "gridExportIncome": 627.315, "pvBenefit": 1937.123, "storageBenefit": 348.575, "totalBenefit": 2285.698}, {"month": 5, "day": 17, "pvGeneration": 38.773, "storageCharge": 14.226, "storageDischarge": 14.841, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 5.289, "gridExportIncome": 0, "pvBenefit": 1229.254, "storageBenefit": 365.737, "totalBenefit": 1594.991}, {"month": 5, "day": 18, "pvGeneration": 25.516, "storageCharge": 2.391, "storageDischarge": 2.223, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 18.331, "gridExportIncome": 0, "pvBenefit": 1002.225, "storageBenefit": 62.392, "totalBenefit": 1064.617}, {"month": 5, "day": 19, "pvGeneration": 40.246, "storageCharge": 15.425, "storageDischarge": 14.243, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 5.564, "gridExportIncome": 0, "pvBenefit": 1256.945, "storageBenefit": 327.316, "totalBenefit": 1584.26}, {"month": 5, "day": 20, "pvGeneration": 108.316, "storageCharge": 16.694, "storageDischarge": 13.992, "electricityConsumption": 43.5, "gridExport": 65.491, "gridImport": 4.486, "gridExportIncome": 982.365, "pvBenefit": 2305.753, "storageBenefit": 296.042, "totalBenefit": 2601.795}, {"month": 5, "day": 21, "pvGeneration": 97.234, "storageCharge": 16.696, "storageDischarge": 15.609, "electricityConsumption": 43.5, "gridExport": 54.684, "gridImport": 3.273, "gridExportIncome": 820.26, "pvBenefit": 2133.655, "storageBenefit": 350.741, "totalBenefit": 2484.396}, {"month": 5, "day": 22, "pvGeneration": 73.418, "storageCharge": 16.695, "storageDischarge": 15.529, "electricityConsumption": 43.5, "gridExport": 30.975, "gridImport": 3.451, "gridExportIncome": 464.625, "pvBenefit": 1774.494, "storageBenefit": 348.092, "totalBenefit": 2122.586}, {"month": 5, "day": 23, "pvGeneration": 55.047, "storageCharge": 16.694, "storageDischarge": 15.659, "electricityConsumption": 43.5, "gridExport": 13.048, "gridImport": 3.777, "gridExportIncome": 195.72, "pvBenefit": 1489.35, "storageBenefit": 353.853, "totalBenefit": 1843.203}, {"month": 5, "day": 24, "pvGeneration": 36.338, "storageCharge": 11.584, "storageDischarge": 12.205, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 7.507, "gridExportIncome": 0, "pvBenefit": 1197.952, "storageBenefit": 316.182, "totalBenefit": 1514.135}, {"month": 5, "day": 25, "pvGeneration": 94.693, "storageCharge": 16.695, "storageDischarge": 13.968, "electricityConsumption": 43.5, "gridExport": 51.934, "gridImport": 4.575, "gridExportIncome": 779.01, "pvBenefit": 2099.347, "storageBenefit": 296.111, "totalBenefit": 2395.457}, {"month": 5, "day": 26, "pvGeneration": 107.044, "storageCharge": 16.695, "storageDischarge": 15.368, "electricityConsumption": 43.5, "gridExport": 64.123, "gridImport": 3.124, "gridExportIncome": 961.845, "pvBenefit": 2289.36, "storageBenefit": 340.976, "totalBenefit": 2630.336}, {"month": 5, "day": 27, "pvGeneration": 63.662, "storageCharge": 17.455, "storageDischarge": 16.929, "electricityConsumption": 43.5, "gridExport": 27.789, "gridImport": 9.498, "gridExportIncome": 416.835, "pvBenefit": 1455.595, "storageBenefit": 389.111, "totalBenefit": 1844.706}, {"month": 5, "day": 28, "pvGeneration": 69.874, "storageCharge": 16.694, "storageDischarge": 14.818, "electricityConsumption": 43.5, "gridExport": 27.174, "gridImport": 3.853, "gridExportIncome": 407.61, "pvBenefit": 1727.904, "storageBenefit": 322.501, "totalBenefit": 2050.405}, {"month": 5, "day": 29, "pvGeneration": 65.564, "storageCharge": 16.695, "storageDischarge": 15.624, "electricityConsumption": 43.5, "gridExport": 27.162, "gridImport": 7.406, "gridExportIncome": 407.43, "pvBenefit": 1583.551, "storageBenefit": 350.373, "totalBenefit": 1933.923}, {"month": 5, "day": 30, "pvGeneration": 80.865, "storageCharge": 16.696, "storageDischarge": 15.335, "electricityConsumption": 43.5, "gridExport": 37.874, "gridImport": 3.083, "gridExportIncome": 568.11, "pvBenefit": 1899.159, "storageBenefit": 338.63, "totalBenefit": 2237.789}, {"month": 5, "day": 31, "pvGeneration": 82.118, "storageCharge": 16.695, "storageDischarge": 15.787, "electricityConsumption": 43.5, "gridExport": 39.329, "gridImport": 2.867, "gridExportIncome": 589.935, "pvBenefit": 1911.5, "storageBenefit": 356.509, "totalBenefit": 2268.009}, {"month": 6, "day": 1, "pvGeneration": 104.035, "storageCharge": 16.697, "storageDischarge": 15.415, "electricityConsumption": 43.5, "gridExport": 61.13, "gridImport": 3.096, "gridExportIncome": 916.95, "pvBenefit": 2243.584, "storageBenefit": 342.86, "totalBenefit": 2586.443}, {"month": 6, "day": 2, "pvGeneration": 107.366, "storageCharge": 16.695, "storageDischarge": 15.496, "electricityConsumption": 43.5, "gridExport": 64.429, "gridImport": 2.99, "gridExportIncome": 966.435, "pvBenefit": 2294.483, "storageBenefit": 345.238, "totalBenefit": 2639.721}, {"month": 6, "day": 3, "pvGeneration": 65.208, "storageCharge": 17.096, "storageDischarge": 16.92, "electricityConsumption": 43.5, "gridExport": 25.213, "gridImport": 5.021, "gridExportIncome": 378.195, "pvBenefit": 1585.804, "storageBenefit": 401.804, "totalBenefit": 1987.608}, {"month": 6, "day": 4, "pvGeneration": 74.723, "storageCharge": 16.873, "storageDischarge": 15.436, "electricityConsumption": 43.5, "gridExport": 32.516, "gridImport": 3.953, "gridExportIncome": 487.74, "pvBenefit": 1778.063, "storageBenefit": 350.653, "totalBenefit": 2128.715}, {"month": 6, "day": 5, "pvGeneration": 44.062, "storageCharge": 16.696, "storageDischarge": 14.946, "electricityConsumption": 43.5, "gridExport": 2.227, "gridImport": 4.594, "gridExportIncome": 33.405, "pvBenefit": 1322.795, "storageBenefit": 328.837, "totalBenefit": 1651.632}, {"month": 6, "day": 6, "pvGeneration": 94.197, "storageCharge": 16.695, "storageDischarge": 15.301, "electricityConsumption": 43.5, "gridExport": 51.41, "gridImport": 3.317, "gridExportIncome": 771.15, "pvBenefit": 2094.726, "storageBenefit": 338.211, "totalBenefit": 2432.937}, {"month": 6, "day": 7, "pvGeneration": 35.153, "storageCharge": 9.798, "storageDischarge": 10.883, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 8.126, "gridExportIncome": 0, "pvBenefit": 1194.515, "storageBenefit": 295.603, "totalBenefit": 1490.118}, {"month": 6, "day": 8, "pvGeneration": 26.683, "storageCharge": 4.235, "storageDischarge": 3.938, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 17.425, "gridExportIncome": 0, "pvBenefit": 1007.941, "storageBenefit": 110.535, "totalBenefit": 1118.476}, {"month": 6, "day": 9, "pvGeneration": 73.577, "storageCharge": 16.695, "storageDischarge": 13.49, "electricityConsumption": 43.5, "gridExport": 32.839, "gridImport": 7.036, "gridExportIncome": 492.585, "pvBenefit": 1750.833, "storageBenefit": 274.983, "totalBenefit": 2025.816}, {"month": 6, "day": 10, "pvGeneration": 37.009, "storageCharge": 12.233, "storageDischarge": 13.415, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 6.374, "gridExportIncome": 0, "pvBenefit": 1207.86, "storageBenefit": 347.274, "totalBenefit": 1555.135}, {"month": 6, "day": 11, "pvGeneration": 42.503, "storageCharge": 16.811, "storageDischarge": 13.865, "electricityConsumption": 43.5, "gridExport": 0.979, "gridImport": 6.019, "gridExportIncome": 14.685, "pvBenefit": 1293.067, "storageBenefit": 289.731, "totalBenefit": 1582.798}, {"month": 6, "day": 12, "pvGeneration": 67.446, "storageCharge": 16.696, "storageDischarge": 15.657, "electricityConsumption": 43.5, "gridExport": 25.049, "gridImport": 3.379, "gridExportIncome": 375.735, "pvBenefit": 1684.926, "storageBenefit": 351.456, "totalBenefit": 2036.382}, {"month": 6, "day": 13, "pvGeneration": 70.652, "storageCharge": 16.695, "storageDischarge": 15.286, "electricityConsumption": 43.5, "gridExport": 28.089, "gridImport": 3.554, "gridExportIncome": 421.335, "pvBenefit": 1738.673, "storageBenefit": 336.479, "totalBenefit": 2075.152}, {"month": 6, "day": 14, "pvGeneration": 67.142, "storageCharge": 16.694, "storageDischarge": 17.41, "electricityConsumption": 43.5, "gridExport": 29.207, "gridImport": 6.23, "gridExportIncome": 438.105, "pvBenefit": 1557.847, "storageBenefit": 450.693, "totalBenefit": 2008.539}, {"month": 6, "day": 15, "pvGeneration": 56.798, "storageCharge": 16.696, "storageDischarge": 13.839, "electricityConsumption": 43.5, "gridExport": 14.667, "gridImport": 5.322, "gridExportIncome": 220.005, "pvBenefit": 1520.865, "storageBenefit": 290.394, "totalBenefit": 1811.258}, {"month": 6, "day": 16, "pvGeneration": 45.106, "storageCharge": 16.696, "storageDischarge": 15.787, "electricityConsumption": 43.5, "gridExport": 3.366, "gridImport": 3.917, "gridExportIncome": 50.49, "pvBenefit": 1335.477, "storageBenefit": 358.085, "totalBenefit": 1693.562}, {"month": 6, "day": 17, "pvGeneration": 51.718, "storageCharge": 16.696, "storageDischarge": 15.447, "electricityConsumption": 43.5, "gridExport": 9.589, "gridImport": 3.841, "gridExportIncome": 143.835, "pvBenefit": 1442.629, "storageBenefit": 345.88, "totalBenefit": 1788.51}, {"month": 6, "day": 18, "pvGeneration": 78.889, "storageCharge": 16.695, "storageDischarge": 15.269, "electricityConsumption": 43.5, "gridExport": 36.056, "gridImport": 3.295, "gridExportIncome": 540.84, "pvBenefit": 1866.058, "storageBenefit": 337.145, "totalBenefit": 2203.203}, {"month": 6, "day": 19, "pvGeneration": 45.981, "storageCharge": 16.693, "storageDischarge": 16.416, "electricityConsumption": 43.5, "gridExport": 4.725, "gridImport": 3.821, "gridExportIncome": 70.875, "pvBenefit": 1333.808, "storageBenefit": 385.049, "totalBenefit": 1718.857}, {"month": 6, "day": 20, "pvGeneration": 42.379, "storageCharge": 18.039, "storageDischarge": 16.109, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 4.324, "gridExportIncome": 0, "pvBenefit": 1269.39, "storageBenefit": 360.98, "totalBenefit": 1630.37}, {"month": 6, "day": 21, "pvGeneration": 57.471, "storageCharge": 16.694, "storageDischarge": 15.678, "electricityConsumption": 43.5, "gridExport": 15.618, "gridImport": 3.903, "gridExportIncome": 234.27, "pvBenefit": 1522.726, "storageBenefit": 354.834, "totalBenefit": 1877.56}, {"month": 6, "day": 22, "pvGeneration": 78.993, "storageCharge": 16.694, "storageDischarge": 15.236, "electricityConsumption": 43.5, "gridExport": 36.055, "gridImport": 3.227, "gridExportIncome": 540.825, "pvBenefit": 1868.539, "storageBenefit": 336.944, "totalBenefit": 2205.483}, {"month": 6, "day": 23, "pvGeneration": 30.422, "storageCharge": 7.474, "storageDischarge": 8.644, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 12.594, "gridExportIncome": 0, "pvBenefit": 1072.827, "storageBenefit": 251.545, "totalBenefit": 1324.372}, {"month": 6, "day": 24, "pvGeneration": 34.674, "storageCharge": 11.197, "storageDischarge": 10.415, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 10.432, "gridExportIncome": 0, "pvBenefit": 1150.514, "storageBenefit": 261.508, "totalBenefit": 1412.022}, {"month": 6, "day": 25, "pvGeneration": 17.194, "storageCharge": 0.289, "storageDischarge": 0.269, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 26.346, "gridExportIncome": 0, "pvBenefit": 718.131, "storageBenefit": 7.555, "totalBenefit": 725.686}, {"month": 6, "day": 26, "pvGeneration": 83.68, "storageCharge": 16.696, "storageDischarge": 13.662, "electricityConsumption": 43.5, "gridExport": 40.628, "gridImport": 4.56, "gridExportIncome": 609.42, "pvBenefit": 1942.904, "storageBenefit": 282.57, "totalBenefit": 2225.474}, {"month": 6, "day": 27, "pvGeneration": 54.149, "storageCharge": 16.694, "storageDischarge": 15.705, "electricityConsumption": 43.5, "gridExport": 11.988, "gridImport": 3.572, "gridExportIncome": 179.82, "pvBenefit": 1481.679, "storageBenefit": 352.562, "totalBenefit": 1834.24}, {"month": 6, "day": 28, "pvGeneration": 98.957, "storageCharge": 16.693, "storageDischarge": 15.463, "electricityConsumption": 43.5, "gridExport": 56.151, "gridImport": 3.147, "gridExportIncome": 842.265, "pvBenefit": 2166.355, "storageBenefit": 343.81, "totalBenefit": 2510.164}, {"month": 6, "day": 29, "pvGeneration": 81.054, "storageCharge": 16.694, "storageDischarge": 15.496, "electricityConsumption": 43.5, "gridExport": 38.43, "gridImport": 3.301, "gridExportIncome": 576.45, "pvBenefit": 1894.791, "storageBenefit": 344.545, "totalBenefit": 2239.336}, {"month": 6, "day": 30, "pvGeneration": 61.416, "storageCharge": 16.695, "storageDischarge": 17.142, "electricityConsumption": 43.5, "gridExport": 21.161, "gridImport": 4.154, "gridExportIncome": 317.415, "pvBenefit": 1539.236, "storageBenefit": 416.945, "totalBenefit": 1956.181}, {"month": 7, "day": 1, "pvGeneration": 43.905, "storageCharge": 16.697, "storageDischarge": 14.513, "electricityConsumption": 43.5, "gridExport": 2.131, "gridImport": 5.058, "gridExportIncome": 31.965, "pvBenefit": 1315.446, "storageBenefit": 318.284, "totalBenefit": 1633.73}, {"month": 7, "day": 2, "pvGeneration": 72.914, "storageCharge": 17.025, "storageDischarge": 16.205, "electricityConsumption": 43.5, "gridExport": 31.745, "gridImport": 4.433, "gridExportIncome": 476.175, "pvBenefit": 1726.062, "storageBenefit": 377.087, "totalBenefit": 2103.149}, {"month": 7, "day": 3, "pvGeneration": 26.262, "storageCharge": 2.629, "storageDischarge": 3.265, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 16.86, "gridExportIncome": 0, "pvBenefit": 1024.165, "storageBenefit": 95.962, "totalBenefit": 1120.127}, {"month": 7, "day": 4, "pvGeneration": 88.554, "storageCharge": 16.694, "storageDischarge": 13.903, "electricityConsumption": 43.5, "gridExport": 45.889, "gridImport": 4.727, "gridExportIncome": 688.335, "pvBenefit": 2006.239, "storageBenefit": 293.253, "totalBenefit": 2299.492}, {"month": 7, "day": 5, "pvGeneration": 87.819, "storageCharge": 16.694, "storageDischarge": 15.173, "electricityConsumption": 43.5, "gridExport": 44.641, "gridImport": 3.046, "gridExportIncome": 669.615, "pvBenefit": 2008.482, "storageBenefit": 331.685, "totalBenefit": 2340.167}, {"month": 7, "day": 6, "pvGeneration": 96.72, "storageCharge": 16.696, "storageDischarge": 15.689, "electricityConsumption": 43.5, "gridExport": 53.7, "gridImport": 2.731, "gridExportIncome": 805.5, "pvBenefit": 2137.304, "storageBenefit": 350.593, "totalBenefit": 2487.896}, {"month": 7, "day": 7, "pvGeneration": 46.794, "storageCharge": 16.694, "storageDischarge": 16.48, "electricityConsumption": 43.5, "gridExport": 5.649, "gridImport": 3.874, "gridExportIncome": 84.735, "pvBenefit": 1343.779, "storageBenefit": 387.34, "totalBenefit": 1731.119}, {"month": 7, "day": 8, "pvGeneration": 62.211, "storageCharge": 16.696, "storageDischarge": 14.705, "electricityConsumption": 43.5, "gridExport": 19.999, "gridImport": 4.443, "gridExportIncome": 299.985, "pvBenefit": 1603.524, "storageBenefit": 319.232, "totalBenefit": 1922.755}, {"month": 7, "day": 9, "pvGeneration": 113.141, "storageCharge": 16.695, "storageDischarge": 15.399, "electricityConsumption": 43.5, "gridExport": 70.139, "gridImport": 3.014, "gridExportIncome": 1052.085, "pvBenefit": 2383.326, "storageBenefit": 340.951, "totalBenefit": 2724.276}, {"month": 7, "day": 10, "pvGeneration": 72.457, "storageCharge": 16.696, "storageDischarge": 15.256, "electricityConsumption": 43.5, "gridExport": 29.241, "gridImport": 2.933, "gridExportIncome": 438.615, "pvBenefit": 1779.929, "storageBenefit": 333.209, "totalBenefit": 2113.138}, {"month": 7, "day": 11, "pvGeneration": 81.162, "storageCharge": 16.694, "storageDischarge": 15.914, "electricityConsumption": 43.5, "gridExport": 38.38, "gridImport": 2.763, "gridExportIncome": 575.7, "pvBenefit": 1898.376, "storageBenefit": 359.347, "totalBenefit": 2257.723}, {"month": 7, "day": 12, "pvGeneration": 78.746, "storageCharge": 16.693, "storageDischarge": 15.318, "electricityConsumption": 43.5, "gridExport": 35.752, "gridImport": 3.093, "gridExportIncome": 536.28, "pvBenefit": 1868.319, "storageBenefit": 337.226, "totalBenefit": 2205.545}, {"month": 7, "day": 13, "pvGeneration": 74.055, "storageCharge": 16.693, "storageDischarge": 14.947, "electricityConsumption": 43.5, "gridExport": 31.374, "gridImport": 3.753, "gridExportIncome": 470.61, "pvBenefit": 1798.533, "storageBenefit": 318.539, "totalBenefit": 2117.072}, {"month": 7, "day": 14, "pvGeneration": 78.611, "storageCharge": 16.695, "storageDischarge": 16.319, "electricityConsumption": 43.5, "gridExport": 35.722, "gridImport": 2.28, "gridExportIncome": 535.83, "pvBenefit": 1862.105, "storageBenefit": 372.818, "totalBenefit": 2234.924}, {"month": 7, "day": 15, "pvGeneration": 75.215, "storageCharge": 16.695, "storageDischarge": 15.415, "electricityConsumption": 43.5, "gridExport": 33.326, "gridImport": 4.112, "gridExportIncome": 499.89, "pvBenefit": 1794.05, "storageBenefit": 341.483, "totalBenefit": 2135.533}, {"month": 7, "day": 16, "pvGeneration": 41.667, "storageCharge": 17.795, "storageDischarge": 17.211, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 3.776, "gridExportIncome": 0, "pvBenefit": 1248.355, "storageBenefit": 403.135, "totalBenefit": 1651.49}, {"month": 7, "day": 17, "pvGeneration": 45.315, "storageCharge": 16.696, "storageDischarge": 15.028, "electricityConsumption": 43.5, "gridExport": 4.396, "gridImport": 5.436, "gridExportIncome": 65.94, "pvBenefit": 1326.069, "storageBenefit": 330.336, "totalBenefit": 1656.406}, {"month": 7, "day": 18, "pvGeneration": 47.175, "storageCharge": 16.695, "storageDischarge": 15.755, "electricityConsumption": 43.5, "gridExport": 5.447, "gridImport": 3.962, "gridExportIncome": 81.705, "pvBenefit": 1366.256, "storageBenefit": 357.035, "totalBenefit": 1723.29}, {"month": 7, "day": 19, "pvGeneration": 42.748, "storageCharge": 16.696, "storageDischarge": 15.221, "electricityConsumption": 43.5, "gridExport": 1.942, "gridImport": 5.373, "gridExportIncome": 29.13, "pvBenefit": 1286.409, "storageBenefit": 335.891, "totalBenefit": 1622.3}, {"month": 7, "day": 20, "pvGeneration": 73.896, "storageCharge": 16.695, "storageDischarge": 16.093, "electricityConsumption": 43.5, "gridExport": 31.551, "gridImport": 3.029, "gridExportIncome": 473.265, "pvBenefit": 1775.641, "storageBenefit": 371.102, "totalBenefit": 2146.744}, {"month": 7, "day": 21, "pvGeneration": 36.399, "storageCharge": 12.791, "storageDischarge": 13.068, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 7.856, "gridExportIncome": 0, "pvBenefit": 1176.479, "storageBenefit": 328.221, "totalBenefit": 1504.7}, {"month": 7, "day": 22, "pvGeneration": 105.089, "storageCharge": 16.694, "storageDischarge": 13.742, "electricityConsumption": 43.5, "gridExport": 62.399, "gridImport": 4.852, "gridExportIncome": 935.985, "pvBenefit": 2256.487, "storageBenefit": 286.136, "totalBenefit": 2542.624}, {"month": 7, "day": 23, "pvGeneration": 100.382, "storageCharge": 16.695, "storageDischarge": 15.672, "electricityConsumption": 43.5, "gridExport": 57.803, "gridImport": 3.186, "gridExportIncome": 867.045, "pvBenefit": 2182.241, "storageBenefit": 351.971, "totalBenefit": 2534.212}, {"month": 7, "day": 24, "pvGeneration": 49.268, "storageCharge": 16.695, "storageDischarge": 15.755, "electricityConsumption": 43.5, "gridExport": 7.608, "gridImport": 4.026, "gridExportIncome": 114.12, "pvBenefit": 1396.305, "storageBenefit": 357.209, "totalBenefit": 1753.514}, {"month": 7, "day": 25, "pvGeneration": 57.513, "storageCharge": 16.694, "storageDischarge": 16.942, "electricityConsumption": 43.5, "gridExport": 18.566, "gridImport": 5.646, "gridExportIncome": 278.49, "pvBenefit": 1434.642, "storageBenefit": 432.481, "totalBenefit": 1867.124}, {"month": 7, "day": 26, "pvGeneration": 42.208, "storageCharge": 16.852, "storageDischarge": 13.919, "electricityConsumption": 43.5, "gridExport": 0.521, "gridImport": 5.847, "gridExportIncome": 7.815, "pvBenefit": 1289.247, "storageBenefit": 291.59, "totalBenefit": 1580.837}, {"month": 7, "day": 27, "pvGeneration": 65.251, "storageCharge": 16.695, "storageDischarge": 15.561, "electricityConsumption": 43.5, "gridExport": 22.657, "gridImport": 3.274, "gridExportIncome": 339.855, "pvBenefit": 1656.463, "storageBenefit": 347.403, "totalBenefit": 2003.866}, {"month": 7, "day": 28, "pvGeneration": 42.075, "storageCharge": 16.695, "storageDischarge": 15.965, "electricityConsumption": 43.5, "gridExport": 0.815, "gridImport": 4.237, "gridExportIncome": 12.225, "pvBenefit": 1279.658, "storageBenefit": 365.608, "totalBenefit": 1645.267}, {"month": 7, "day": 29, "pvGeneration": 62.606, "storageCharge": 16.695, "storageDischarge": 15.35, "electricityConsumption": 43.5, "gridExport": 20.897, "gridImport": 4.351, "gridExportIncome": 313.455, "pvBenefit": 1597.777, "storageBenefit": 343.199, "totalBenefit": 1940.976}, {"month": 7, "day": 30, "pvGeneration": 41.05, "storageCharge": 17.213, "storageDischarge": 16.447, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 4.518, "gridExportIncome": 0, "pvBenefit": 1242.815, "storageBenefit": 381.966, "totalBenefit": 1624.781}, {"month": 7, "day": 31, "pvGeneration": 42.774, "storageCharge": 16.696, "storageDischarge": 15.201, "electricityConsumption": 43.5, "gridExport": 1.503, "gridImport": 4.924, "gridExportIncome": 22.545, "pvBenefit": 1291.097, "storageBenefit": 339.444, "totalBenefit": 1630.541}, {"month": 8, "day": 1, "pvGeneration": 35.845, "storageCharge": 11.593, "storageDischarge": 12.131, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 8.074, "gridExportIncome": 0, "pvBenefit": 1179.816, "storageBenefit": 315.164, "totalBenefit": 1494.98}, {"month": 8, "day": 2, "pvGeneration": 54.948, "storageCharge": 17.113, "storageDischarge": 14.179, "electricityConsumption": 43.5, "gridExport": 15.691, "gridImport": 8.301, "gridExportIncome": 235.365, "pvBenefit": 1433.394, "storageBenefit": 294.938, "totalBenefit": 1728.332}, {"month": 8, "day": 3, "pvGeneration": 50.193, "storageCharge": 16.695, "storageDischarge": 15.77, "electricityConsumption": 43.5, "gridExport": 8.434, "gridImport": 3.916, "gridExportIncome": 126.51, "pvBenefit": 1412.809, "storageBenefit": 356.826, "totalBenefit": 1769.635}, {"month": 8, "day": 4, "pvGeneration": 53.804, "storageCharge": 16.694, "storageDischarge": 15.577, "electricityConsumption": 43.5, "gridExport": 12.191, "gridImport": 4.238, "gridExportIncome": 182.865, "pvBenefit": 1463.816, "storageBenefit": 350.948, "totalBenefit": 1814.764}, {"month": 8, "day": 5, "pvGeneration": 70.092, "storageCharge": 16.696, "storageDischarge": 15.448, "electricityConsumption": 43.5, "gridExport": 28.238, "gridImport": 4.116, "gridExportIncome": 423.57, "pvBenefit": 1713.418, "storageBenefit": 345.739, "totalBenefit": 2059.157}, {"month": 8, "day": 6, "pvGeneration": 60.454, "storageCharge": 17.094, "storageDischarge": 15.867, "electricityConsumption": 43.5, "gridExport": 20.897, "gridImport": 6.427, "gridExportIncome": 313.455, "pvBenefit": 1516.231, "storageBenefit": 353.362, "totalBenefit": 1869.593}, {"month": 8, "day": 7, "pvGeneration": 74.062, "storageCharge": 16.695, "storageDischarge": 15.368, "electricityConsumption": 43.5, "gridExport": 32.357, "gridImport": 4.342, "gridExportIncome": 485.355, "pvBenefit": 1772.341, "storageBenefit": 340.976, "totalBenefit": 2113.317}, {"month": 8, "day": 8, "pvGeneration": 84.09, "storageCharge": 16.694, "storageDischarge": 15.545, "electricityConsumption": 43.5, "gridExport": 41.907, "gridImport": 3.698, "gridExportIncome": 628.605, "pvBenefit": 1931.389, "storageBenefit": 347.059, "totalBenefit": 2278.448}, {"month": 8, "day": 9, "pvGeneration": 93.698, "storageCharge": 16.696, "storageDischarge": 15.544, "electricityConsumption": 43.5, "gridExport": 51.536, "gridImport": 3.72, "gridExportIncome": 773.04, "pvBenefit": 2074.932, "storageBenefit": 347.17, "totalBenefit": 2422.102}, {"month": 8, "day": 10, "pvGeneration": 88.567, "storageCharge": 16.695, "storageDischarge": 15.657, "electricityConsumption": 43.5, "gridExport": 47.002, "gridImport": 4.214, "gridExportIncome": 705.03, "pvBenefit": 1985.588, "storageBenefit": 352.354, "totalBenefit": 2337.942}, {"month": 8, "day": 11, "pvGeneration": 61.893, "storageCharge": 17.2, "storageDischarge": 15.757, "electricityConsumption": 43.5, "gridExport": 20.245, "gridImport": 4.541, "gridExportIncome": 303.675, "pvBenefit": 1575.344, "storageBenefit": 350.595, "totalBenefit": 1925.938}, {"month": 8, "day": 12, "pvGeneration": 48.731, "storageCharge": 17.121, "storageDischarge": 16.213, "electricityConsumption": 43.5, "gridExport": 9.034, "gridImport": 5.992, "gridExportIncome": 135.51, "pvBenefit": 1345.56, "storageBenefit": 365.013, "totalBenefit": 1710.573}, {"month": 8, "day": 13, "pvGeneration": 52.619, "storageCharge": 16.696, "storageDischarge": 15.657, "electricityConsumption": 43.5, "gridExport": 11.39, "gridImport": 4.552, "gridExportIncome": 170.85, "pvBenefit": 1438.279, "storageBenefit": 354.28, "totalBenefit": 1792.559}, {"month": 8, "day": 14, "pvGeneration": 85.777, "storageCharge": 16.694, "storageDischarge": 15.318, "electricityConsumption": 43.5, "gridExport": 44.164, "gridImport": 4.475, "gridExportIncome": 662.46, "pvBenefit": 1945.002, "storageBenefit": 340.732, "totalBenefit": 2285.734}, {"month": 8, "day": 15, "pvGeneration": 66.644, "storageCharge": 16.694, "storageDischarge": 15.578, "electricityConsumption": 43.5, "gridExport": 25.19, "gridImport": 4.399, "gridExportIncome": 377.85, "pvBenefit": 1654.556, "storageBenefit": 349.924, "totalBenefit": 2004.48}, {"month": 8, "day": 16, "pvGeneration": 67.382, "storageCharge": 17.319, "storageDischarge": 17.545, "electricityConsumption": 43.5, "gridExport": 27.468, "gridImport": 4.748, "gridExportIncome": 412.02, "pvBenefit": 1604.028, "storageBenefit": 428.035, "totalBenefit": 2032.064}, {"month": 8, "day": 17, "pvGeneration": 83.719, "storageCharge": 16.695, "storageDischarge": 14.044, "electricityConsumption": 43.5, "gridExport": 42.236, "gridImport": 5.778, "gridExportIncome": 633.54, "pvBenefit": 1911.79, "storageBenefit": 298.293, "totalBenefit": 2210.082}, {"month": 8, "day": 18, "pvGeneration": 58.004, "storageCharge": 16.694, "storageDischarge": 15.512, "electricityConsumption": 43.5, "gridExport": 16.452, "gridImport": 4.362, "gridExportIncome": 246.78, "pvBenefit": 1527.41, "storageBenefit": 347.007, "totalBenefit": 1874.417}, {"month": 8, "day": 19, "pvGeneration": 77.445, "storageCharge": 16.695, "storageDischarge": 15.642, "electricityConsumption": 43.5, "gridExport": 35.993, "gridImport": 4.34, "gridExportIncome": 539.895, "pvBenefit": 1815.989, "storageBenefit": 352.563, "totalBenefit": 2168.552}, {"month": 8, "day": 20, "pvGeneration": 32.146, "storageCharge": 9.295, "storageDischarge": 10.14, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 11.31, "gridExportIncome": 0, "pvBenefit": 1097.766, "storageBenefit": 284.216, "totalBenefit": 1381.982}, {"month": 8, "day": 21, "pvGeneration": 71.214, "storageCharge": 16.695, "storageDischarge": 14.582, "electricityConsumption": 43.5, "gridExport": 30.438, "gridImport": 5.993, "gridExportIncome": 456.57, "pvBenefit": 1704.18, "storageBenefit": 323.249, "totalBenefit": 2027.43}, {"month": 8, "day": 22, "pvGeneration": 81.328, "storageCharge": 16.697, "storageDischarge": 14.945, "electricityConsumption": 43.5, "gridExport": 39.89, "gridImport": 4.996, "gridExportIncome": 598.35, "pvBenefit": 1874.349, "storageBenefit": 328.964, "totalBenefit": 2203.312}, {"month": 8, "day": 23, "pvGeneration": 39.222, "storageCharge": 15.969, "storageDischarge": 15.835, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 5.663, "gridExportIncome": 0, "pvBenefit": 1212.171, "storageBenefit": 372.867, "totalBenefit": 1585.038}, {"month": 8, "day": 24, "pvGeneration": 34.126, "storageCharge": 11.324, "storageDischarge": 11.079, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 10.495, "gridExportIncome": 0, "pvBenefit": 1126.413, "storageBenefit": 285.224, "totalBenefit": 1411.637}, {"month": 8, "day": 25, "pvGeneration": 79.495, "storageCharge": 16.693, "storageDischarge": 14, "electricityConsumption": 43.5, "gridExport": 38.106, "gridImport": 5.915, "gridExportIncome": 571.59, "pvBenefit": 1845.975, "storageBenefit": 297.555, "totalBenefit": 2143.53}, {"month": 8, "day": 26, "pvGeneration": 87.919, "storageCharge": 16.694, "storageDischarge": 15.512, "electricityConsumption": 43.5, "gridExport": 46.485, "gridImport": 4.474, "gridExportIncome": 697.275, "pvBenefit": 1973.388, "storageBenefit": 347.715, "totalBenefit": 2321.103}, {"month": 8, "day": 27, "pvGeneration": 44.932, "storageCharge": 16.696, "storageDischarge": 15.932, "electricityConsumption": 43.5, "gridExport": 4.027, "gridImport": 4.62, "gridExportIncome": 60.405, "pvBenefit": 1314.407, "storageBenefit": 366.075, "totalBenefit": 1680.482}, {"month": 8, "day": 28, "pvGeneration": 69.19, "storageCharge": 16.695, "storageDischarge": 15.124, "electricityConsumption": 43.5, "gridExport": 27.979, "gridImport": 5.058, "gridExportIncome": 419.685, "pvBenefit": 1688.317, "storageBenefit": 334.78, "totalBenefit": 2023.097}, {"month": 8, "day": 29, "pvGeneration": 67.879, "storageCharge": 16.695, "storageDischarge": 15.587, "electricityConsumption": 43.5, "gridExport": 26.565, "gridImport": 4.528, "gridExportIncome": 398.475, "pvBenefit": 1669.894, "storageBenefit": 350.841, "totalBenefit": 2020.735}, {"month": 8, "day": 30, "pvGeneration": 83.637, "storageCharge": 16.695, "storageDischarge": 15.58, "electricityConsumption": 43.5, "gridExport": 42.375, "gridImport": 4.586, "gridExportIncome": 635.625, "pvBenefit": 1904.746, "storageBenefit": 351.175, "totalBenefit": 2255.92}, {"month": 8, "day": 31, "pvGeneration": 67.225, "storageCharge": 16.695, "storageDischarge": 15.433, "electricityConsumption": 43.5, "gridExport": 25.907, "gridImport": 4.659, "gridExportIncome": 388.605, "pvBenefit": 1660.717, "storageBenefit": 345.244, "totalBenefit": 2005.961}, {"month": 9, "day": 1, "pvGeneration": 37.365, "storageCharge": 13.779, "storageDischarge": 14.343, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 6.705, "gridExportIncome": 0, "pvBenefit": 1192.841, "storageBenefit": 353.613, "totalBenefit": 1546.454}, {"month": 9, "day": 2, "pvGeneration": 52.741, "storageCharge": 16.694, "storageDischarge": 14.645, "electricityConsumption": 43.5, "gridExport": 12.677, "gridImport": 6.644, "gridExportIncome": 190.155, "pvBenefit": 1413.358, "storageBenefit": 326.049, "totalBenefit": 1739.407}, {"month": 9, "day": 3, "pvGeneration": 22.672, "storageCharge": 1.041, "storageDischarge": 1.851, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 20.167, "gridExportIncome": 0, "pvBenefit": 922.786, "storageBenefit": 56.585, "totalBenefit": 979.371}, {"month": 9, "day": 4, "pvGeneration": 23.1, "storageCharge": 1.767, "storageDischarge": 1.645, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 20.652, "gridExportIncome": 0, "pvBenefit": 927.488, "storageBenefit": 46.204, "totalBenefit": 973.692}, {"month": 9, "day": 5, "pvGeneration": 61.175, "storageCharge": 16.694, "storageDischarge": 15.21, "electricityConsumption": 43.5, "gridExport": 21.096, "gridImport": 6.107, "gridExportIncome": 316.44, "pvBenefit": 1534.05, "storageBenefit": 351.022, "totalBenefit": 1885.072}, {"month": 9, "day": 6, "pvGeneration": 49.656, "storageCharge": 16.695, "storageDischarge": 15.236, "electricityConsumption": 43.5, "gridExport": 9.27, "gridImport": 5.776, "gridExportIncome": 139.05, "pvBenefit": 1370.026, "storageBenefit": 348.701, "totalBenefit": 1718.727}, {"month": 9, "day": 7, "pvGeneration": 45.607, "storageCharge": 16.695, "storageDischarge": 15.383, "electricityConsumption": 43.5, "gridExport": 5.641, "gridImport": 6.062, "gridExportIncome": 84.615, "pvBenefit": 1303.233, "storageBenefit": 352.016, "totalBenefit": 1655.248}, {"month": 9, "day": 8, "pvGeneration": 17.806, "storageCharge": 0.321, "storageDischarge": 1.053, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 25.046, "gridExportIncome": 0, "pvBenefit": 743.724, "storageBenefit": 33.52, "totalBenefit": 777.244}, {"month": 9, "day": 9, "pvGeneration": 19.546, "storageCharge": 0.48, "storageDischarge": 0.447, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 24.025, "gridExportIncome": 0, "pvBenefit": 809.696, "storageBenefit": 12.557, "totalBenefit": 822.254}, {"month": 9, "day": 10, "pvGeneration": 82.952, "storageCharge": 16.695, "storageDischarge": 14.597, "electricityConsumption": 43.5, "gridExport": 42.26, "gridImport": 6.058, "gridExportIncome": 633.9, "pvBenefit": 1878.575, "storageBenefit": 323.912, "totalBenefit": 2202.488}, {"month": 9, "day": 11, "pvGeneration": 52.739, "storageCharge": 16.696, "storageDischarge": 15.867, "electricityConsumption": 43.5, "gridExport": 12.367, "gridImport": 5.212, "gridExportIncome": 185.505, "pvBenefit": 1415.803, "storageBenefit": 369.883, "totalBenefit": 1785.687}, {"month": 9, "day": 12, "pvGeneration": 59.969, "storageCharge": 16.695, "storageDischarge": 15.399, "electricityConsumption": 43.5, "gridExport": 20, "gridImport": 6.047, "gridExportIncome": 300, "pvBenefit": 1518.314, "storageBenefit": 352.908, "totalBenefit": 1871.222}, {"month": 9, "day": 13, "pvGeneration": 68.803, "storageCharge": 16.694, "storageDischarge": 15.512, "electricityConsumption": 43.5, "gridExport": 28.365, "gridImport": 5.474, "gridExportIncome": 425.475, "pvBenefit": 1659.563, "storageBenefit": 356.512, "totalBenefit": 2016.074}, {"month": 9, "day": 14, "pvGeneration": 17.71, "storageCharge": 0.289, "storageDischarge": 1.006, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 25.153, "gridExportIncome": 0, "pvBenefit": 742.159, "storageBenefit": 32.097, "totalBenefit": 774.256}, {"month": 9, "day": 15, "pvGeneration": 23.658, "storageCharge": 2.621, "storageDischarge": 2.435, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 20.221, "gridExportIncome": 0, "pvBenefit": 927.621, "storageBenefit": 68.312, "totalBenefit": 995.933}, {"month": 9, "day": 16, "pvGeneration": 42.787, "storageCharge": 17.18, "storageDischarge": 15.13, "electricityConsumption": 43.5, "gridExport": 3.385, "gridImport": 7.346, "gridExportIncome": 50.775, "pvBenefit": 1237.801, "storageBenefit": 340.196, "totalBenefit": 1577.997}, {"month": 9, "day": 17, "pvGeneration": 82.672, "storageCharge": 16.695, "storageDischarge": 15.657, "electricityConsumption": 43.5, "gridExport": 42.221, "gridImport": 5.326, "gridExportIncome": 633.315, "pvBenefit": 1867.716, "storageBenefit": 361.499, "totalBenefit": 2229.215}, {"month": 9, "day": 18, "pvGeneration": 29.319, "storageCharge": 6.648, "storageDischarge": 6.902, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 14.47, "gridExportIncome": 0, "pvBenefit": 1043.703, "storageBenefit": 197.489, "totalBenefit": 1241.193}, {"month": 9, "day": 19, "pvGeneration": 90.55, "storageCharge": 16.696, "storageDischarge": 14.871, "electricityConsumption": 43.5, "gridExport": 50.163, "gridImport": 6.113, "gridExportIncome": 752.445, "pvBenefit": 1983.959, "storageBenefit": 336.008, "totalBenefit": 2319.967}, {"month": 9, "day": 20, "pvGeneration": 50.462, "storageCharge": 19.975, "storageDischarge": 18.579, "electricityConsumption": 43.5, "gridExport": 9.813, "gridImport": 5.716, "gridExportIncome": 147.195, "pvBenefit": 1294.106, "storageBenefit": 443.556, "totalBenefit": 1737.662}, {"month": 9, "day": 21, "pvGeneration": 52.187, "storageCharge": 16.694, "storageDischarge": 15.625, "electricityConsumption": 43.5, "gridExport": 11.968, "gridImport": 5.588, "gridExportIncome": 179.52, "pvBenefit": 1404.393, "storageBenefit": 362.204, "totalBenefit": 1766.597}, {"month": 9, "day": 22, "pvGeneration": 53.737, "storageCharge": 16.693, "storageDischarge": 15.527, "electricityConsumption": 43.5, "gridExport": 13.688, "gridImport": 5.843, "gridExportIncome": 205.32, "pvBenefit": 1424.605, "storageBenefit": 358.955, "totalBenefit": 1783.56}, {"month": 9, "day": 23, "pvGeneration": 69.536, "storageCharge": 16.695, "storageDischarge": 15.462, "electricityConsumption": 43.5, "gridExport": 29.216, "gridImport": 5.632, "gridExportIncome": 438.24, "pvBenefit": 1667.229, "storageBenefit": 356.052, "totalBenefit": 2023.281}, {"month": 9, "day": 24, "pvGeneration": 53.047, "storageCharge": 16.694, "storageDischarge": 15.463, "electricityConsumption": 43.5, "gridExport": 13.611, "gridImport": 6.52, "gridExportIncome": 204.165, "pvBenefit": 1404.363, "storageBenefit": 355.403, "totalBenefit": 1759.766}, {"month": 9, "day": 25, "pvGeneration": 68.974, "storageCharge": 16.695, "storageDischarge": 15.625, "electricityConsumption": 43.5, "gridExport": 28.698, "gridImport": 5.532, "gridExportIncome": 430.47, "pvBenefit": 1657.572, "storageBenefit": 361.84, "totalBenefit": 2019.411}, {"month": 9, "day": 26, "pvGeneration": 46.084, "storageCharge": 16.694, "storageDischarge": 15.576, "electricityConsumption": 43.5, "gridExport": 6.357, "gridImport": 6.126, "gridExportIncome": 95.355, "pvBenefit": 1303.633, "storageBenefit": 360.746, "totalBenefit": 1664.38}, {"month": 9, "day": 27, "pvGeneration": 24.447, "storageCharge": 2.353, "storageDischarge": 2.732, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 18.891, "gridExportIncome": 0, "pvBenefit": 956.129, "storageBenefit": 79.53, "totalBenefit": 1035.659}, {"month": 9, "day": 28, "pvGeneration": 18.79, "storageCharge": 0.448, "storageDischarge": 0.417, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 24.776, "gridExportIncome": 0, "pvBenefit": 787.243, "storageBenefit": 11.711, "totalBenefit": 798.955}, {"month": 9, "day": 29, "pvGeneration": 71.661, "storageCharge": 16.695, "storageDischarge": 14.968, "electricityConsumption": 43.5, "gridExport": 31.418, "gridImport": 6.168, "gridExportIncome": 471.27, "pvBenefit": 1696.942, "storageBenefit": 340.311, "totalBenefit": 2037.253}, {"month": 9, "day": 30, "pvGeneration": 74.819, "storageCharge": 16.695, "storageDischarge": 15.527, "electricityConsumption": 43.5, "gridExport": 34.574, "gridImport": 5.653, "gridExportIncome": 518.61, "pvBenefit": 1744.312, "storageBenefit": 358.925, "totalBenefit": 2103.237}, {"month": 10, "day": 1, "pvGeneration": 64.962, "storageCharge": 16.693, "storageDischarge": 15.543, "electricityConsumption": 43.5, "gridExport": 24.736, "gridImport": 5.655, "gridExportIncome": 371.04, "pvBenefit": 1595.99, "storageBenefit": 359.662, "totalBenefit": 1955.652}, {"month": 10, "day": 2, "pvGeneration": 54.043, "storageCharge": 16.694, "storageDischarge": 15.528, "electricityConsumption": 43.5, "gridExport": 14.279, "gridImport": 6.136, "gridExportIncome": 214.185, "pvBenefit": 1423.659, "storageBenefit": 359.148, "totalBenefit": 1782.807}, {"month": 10, "day": 3, "pvGeneration": 61.553, "storageCharge": 16.695, "storageDischarge": 15.528, "electricityConsumption": 43.5, "gridExport": 21.584, "gridImport": 5.926, "gridExportIncome": 323.76, "pvBenefit": 1540.134, "storageBenefit": 359.133, "totalBenefit": 1899.267}, {"month": 10, "day": 4, "pvGeneration": 47.95, "storageCharge": 16.696, "storageDischarge": 15.621, "electricityConsumption": 43.5, "gridExport": 8.192, "gridImport": 6.052, "gridExportIncome": 122.88, "pvBenefit": 1331.232, "storageBenefit": 363.229, "totalBenefit": 1694.461}, {"month": 10, "day": 5, "pvGeneration": 38.007, "storageCharge": 16.696, "storageDischarge": 15.418, "electricityConsumption": 43.5, "gridExport": 0.703, "gridImport": 8.691, "gridExportIncome": 10.545, "pvBenefit": 1138.33, "storageBenefit": 355.281, "totalBenefit": 1493.611}, {"month": 10, "day": 6, "pvGeneration": 40.383, "storageCharge": 16.813, "storageDischarge": 16.196, "electricityConsumption": 43.5, "gridExport": 5.429, "gridImport": 10.441, "gridExportIncome": 81.435, "pvBenefit": 1097.886, "storageBenefit": 410.378, "totalBenefit": 1508.265}, {"month": 10, "day": 7, "pvGeneration": 42.959, "storageCharge": 16.694, "storageDischarge": 14.919, "electricityConsumption": 43.5, "gridExport": 4.814, "gridImport": 8.31, "gridExportIncome": 72.21, "pvBenefit": 1228.542, "storageBenefit": 338.16, "totalBenefit": 1566.702}, {"month": 10, "day": 8, "pvGeneration": 76.098, "storageCharge": 16.696, "storageDischarge": 15.609, "electricityConsumption": 43.5, "gridExport": 35.886, "gridImport": 5.611, "gridExportIncome": 538.29, "pvBenefit": 1762.563, "storageBenefit": 361.99, "totalBenefit": 2124.552}, {"month": 10, "day": 9, "pvGeneration": 10.618, "storageCharge": 0, "storageDischarge": 0.528, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 32.396, "gridExportIncome": 0, "pvBenefit": 450.001, "storageBenefit": 17.582, "totalBenefit": 467.583}, {"month": 10, "day": 10, "pvGeneration": 72.299, "storageCharge": 16.696, "storageDischarge": 15, "electricityConsumption": 43.5, "gridExport": 32.104, "gridImport": 6.187, "gridExportIncome": 481.56, "pvBenefit": 1705.285, "storageBenefit": 341.71, "totalBenefit": 2046.995}, {"month": 10, "day": 11, "pvGeneration": 54.438, "storageCharge": 16.96, "storageDischarge": 16.3, "electricityConsumption": 43.5, "gridExport": 14.976, "gridImport": 5.988, "gridExportIncome": 224.64, "pvBenefit": 1411.56, "storageBenefit": 388.605, "totalBenefit": 1800.165}, {"month": 10, "day": 12, "pvGeneration": 57.263, "storageCharge": 16.695, "storageDischarge": 15, "electricityConsumption": 43.5, "gridExport": 17.499, "gridImport": 6.617, "gridExportIncome": 262.485, "pvBenefit": 1471.876, "storageBenefit": 341.725, "totalBenefit": 1813.601}, {"month": 10, "day": 13, "pvGeneration": 53.208, "storageCharge": 16.694, "storageDischarge": 15.528, "electricityConsumption": 43.5, "gridExport": 13.931, "gridImport": 6.621, "gridExportIncome": 208.965, "pvBenefit": 1402.084, "storageBenefit": 359.322, "totalBenefit": 1761.406}, {"month": 10, "day": 14, "pvGeneration": 48.27, "storageCharge": 16.695, "storageDischarge": 15.528, "electricityConsumption": 43.5, "gridExport": 8.684, "gridImport": 6.307, "gridExportIncome": 130.26, "pvBenefit": 1333.76, "storageBenefit": 359.307, "totalBenefit": 1693.068}, {"month": 10, "day": 15, "pvGeneration": 61.769, "storageCharge": 16.694, "storageDischarge": 15.98, "electricityConsumption": 43.5, "gridExport": 22.582, "gridImport": 6.292, "gridExportIncome": 338.73, "pvBenefit": 1523.998, "storageBenefit": 379.301, "totalBenefit": 1903.299}, {"month": 10, "day": 16, "pvGeneration": 47.966, "storageCharge": 16.696, "storageDischarge": 15.075, "electricityConsumption": 43.5, "gridExport": 8.771, "gridImport": 7.118, "gridExportIncome": 131.565, "pvBenefit": 1321.99, "storageBenefit": 344.208, "totalBenefit": 1666.197}, {"month": 10, "day": 17, "pvGeneration": 57.69, "storageCharge": 16.696, "storageDischarge": 15.528, "electricityConsumption": 43.5, "gridExport": 17.72, "gridImport": 5.925, "gridExportIncome": 265.8, "pvBenefit": 1482.069, "storageBenefit": 359.292, "totalBenefit": 1841.362}, {"month": 10, "day": 18, "pvGeneration": 27.246, "storageCharge": 6.311, "storageDischarge": 6.398, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 16.676, "gridExportIncome": 0, "pvBenefit": 967.192, "storageBenefit": 182.208, "totalBenefit": 1149.4}, {"month": 10, "day": 19, "pvGeneration": 17.112, "storageCharge": 0.062, "storageDischarge": 0.058, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 26.395, "gridExportIncome": 0, "pvBenefit": 723.453, "storageBenefit": 1.634, "totalBenefit": 725.087}, {"month": 10, "day": 20, "pvGeneration": 22.476, "storageCharge": 2.258, "storageDischarge": 2.098, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 21.347, "gridExportIncome": 0, "pvBenefit": 887.692, "storageBenefit": 58.862, "totalBenefit": 946.553}, {"month": 10, "day": 21, "pvGeneration": 22.834, "storageCharge": 3.097, "storageDischarge": 2.88, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 21.111, "gridExportIncome": 0, "pvBenefit": 872.236, "storageBenefit": 80.841, "totalBenefit": 953.077}, {"month": 10, "day": 22, "pvGeneration": 20.436, "storageCharge": 1.529, "storageDischarge": 1.421, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 23.286, "gridExportIncome": 0, "pvBenefit": 823.832, "storageBenefit": 39.873, "totalBenefit": 863.705}, {"month": 10, "day": 23, "pvGeneration": 18.833, "storageCharge": 0.402, "storageDischarge": 0.373, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 24.723, "gridExportIncome": 0, "pvBenefit": 784.143, "storageBenefit": 10.457, "totalBenefit": 794.6}, {"month": 10, "day": 24, "pvGeneration": 21.032, "storageCharge": 1.554, "storageDischarge": 1.444, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 22.689, "gridExportIncome": 0, "pvBenefit": 851.443, "storageBenefit": 40.515, "totalBenefit": 891.958}, {"month": 10, "day": 25, "pvGeneration": 27.789, "storageCharge": 11.975, "storageDischarge": 11.136, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 17.429, "gridExportIncome": 0, "pvBenefit": 868.772, "storageBenefit": 291.985, "totalBenefit": 1160.757}, {"month": 10, "day": 26, "pvGeneration": 32.355, "storageCharge": 11.232, "storageDischarge": 10.447, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 12.756, "gridExportIncome": 0, "pvBenefit": 1052.424, "storageBenefit": 282.257, "totalBenefit": 1334.681}, {"month": 10, "day": 27, "pvGeneration": 41.136, "storageCharge": 16.696, "storageDischarge": 15.527, "electricityConsumption": 43.5, "gridExport": 3.042, "gridImport": 7.799, "gridExportIncome": 45.63, "pvBenefit": 1189.011, "storageBenefit": 369.647, "totalBenefit": 1558.658}, {"month": 10, "day": 28, "pvGeneration": 36.905, "storageCharge": 15.681, "storageDischarge": 14.583, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 8.848, "gridExportIncome": 0, "pvBenefit": 1125.334, "storageBenefit": 350.439, "totalBenefit": 1475.773}, {"month": 10, "day": 29, "pvGeneration": 22.711, "storageCharge": 5.167, "storageDischarge": 4.804, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 21.531, "gridExportIncome": 0, "pvBenefit": 813.579, "storageBenefit": 134.832, "totalBenefit": 948.411}, {"month": 10, "day": 30, "pvGeneration": 52.736, "storageCharge": 16.695, "storageDischarge": 15.527, "electricityConsumption": 43.5, "gridExport": 14.427, "gridImport": 7.589, "gridExportIncome": 216.405, "pvBenefit": 1368.366, "storageBenefit": 368.245, "totalBenefit": 1736.611}, {"month": 10, "day": 31, "pvGeneration": 18.719, "storageCharge": 0.423, "storageDischarge": 0.394, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 24.84, "gridExportIncome": 0, "pvBenefit": 788.498, "storageBenefit": 11.07, "totalBenefit": 799.567}, {"month": 11, "day": 1, "pvGeneration": 13.872, "storageCharge": 0, "storageDischarge": 0, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 29.625, "gridExportIncome": 0, "pvBenefit": 587.68, "storageBenefit": 0, "totalBenefit": 587.68}, {"month": 11, "day": 2, "pvGeneration": 36.092, "storageCharge": 15.935, "storageDischarge": 14.82, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 9.697, "gridExportIncome": 0, "pvBenefit": 1079.163, "storageBenefit": 368.953, "totalBenefit": 1448.116}, {"month": 11, "day": 3, "pvGeneration": 21.222, "storageCharge": 8.05, "storageDischarge": 7.486, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 23.436, "gridExportIncome": 0, "pvBenefit": 689.436, "storageBenefit": 210.131, "totalBenefit": 899.568}, {"month": 11, "day": 4, "pvGeneration": 56.403, "storageCharge": 16.695, "storageDischarge": 15.526, "electricityConsumption": 43.5, "gridExport": 18.339, "gridImport": 7.83, "gridExportIncome": 275.085, "pvBenefit": 1418.012, "storageBenefit": 369.084, "totalBenefit": 1787.096}, {"month": 11, "day": 5, "pvGeneration": 31.218, "storageCharge": 12.096, "storageDischarge": 11.249, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 14.017, "gridExportIncome": 0, "pvBenefit": 980.286, "storageBenefit": 314.469, "totalBenefit": 1294.754}, {"month": 11, "day": 6, "pvGeneration": 44.732, "storageCharge": 16.697, "storageDischarge": 15.527, "electricityConsumption": 43.5, "gridExport": 7.164, "gridImport": 8.331, "gridExportIncome": 107.46, "pvBenefit": 1233.136, "storageBenefit": 369.795, "totalBenefit": 1602.932}, {"month": 11, "day": 7, "pvGeneration": 49.079, "storageCharge": 16.694, "storageDischarge": 15.527, "electricityConsumption": 43.5, "gridExport": 11.062, "gridImport": 7.879, "gridExportIncome": 165.93, "pvBenefit": 1307.448, "storageBenefit": 368.957, "totalBenefit": 1676.406}, {"month": 11, "day": 8, "pvGeneration": 44.182, "storageCharge": 16.696, "storageDischarge": 15.527, "electricityConsumption": 43.5, "gridExport": 6.277, "gridImport": 7.992, "gridExportIncome": 94.155, "pvBenefit": 1231.072, "storageBenefit": 369.81, "totalBenefit": 1600.882}, {"month": 11, "day": 9, "pvGeneration": 45.8, "storageCharge": 16.695, "storageDischarge": 15.527, "electricityConsumption": 43.5, "gridExport": 7.894, "gridImport": 7.992, "gridExportIncome": 118.41, "pvBenefit": 1255.342, "storageBenefit": 369.825, "totalBenefit": 1625.167}, {"month": 11, "day": 10, "pvGeneration": 45.653, "storageCharge": 16.694, "storageDischarge": 15.527, "electricityConsumption": 43.5, "gridExport": 7.812, "gridImport": 8.055, "gridExportIncome": 117.18, "pvBenefit": 1253.226, "storageBenefit": 368.609, "totalBenefit": 1621.835}, {"month": 11, "day": 11, "pvGeneration": 51.226, "storageCharge": 16.695, "storageDischarge": 15.528, "electricityConsumption": 43.5, "gridExport": 13.274, "gridImport": 7.944, "gridExportIncome": 199.11, "pvBenefit": 1337.959, "storageBenefit": 369.51, "totalBenefit": 1707.469}, {"month": 11, "day": 12, "pvGeneration": 29.386, "storageCharge": 8.848, "storageDischarge": 8.228, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 15.383, "gridExportIncome": 0, "pvBenefit": 996.62, "storageBenefit": 230.958, "totalBenefit": 1227.577}, {"month": 11, "day": 13, "pvGeneration": 49.67, "storageCharge": 16.694, "storageDischarge": 15.527, "electricityConsumption": 43.5, "gridExport": 11.718, "gridImport": 7.944, "gridExportIncome": 175.77, "pvBenefit": 1314.793, "storageBenefit": 369.306, "totalBenefit": 1684.1}, {"month": 11, "day": 14, "pvGeneration": 38.894, "storageCharge": 16.695, "storageDischarge": 15.526, "electricityConsumption": 43.5, "gridExport": 1.569, "gridImport": 8.57, "gridExportIncome": 23.535, "pvBenefit": 1141.835, "storageBenefit": 369.083, "totalBenefit": 1510.919}, {"month": 11, "day": 15, "pvGeneration": 53.841, "storageCharge": 16.696, "storageDischarge": 15.528, "electricityConsumption": 43.5, "gridExport": 15.972, "gridImport": 8.024, "gridExportIncome": 239.58, "pvBenefit": 1375.683, "storageBenefit": 369.495, "totalBenefit": 1745.178}, {"month": 11, "day": 16, "pvGeneration": 51.634, "storageCharge": 16.697, "storageDischarge": 15.527, "electricityConsumption": 43.5, "gridExport": 13.776, "gridImport": 8.04, "gridExportIncome": 206.64, "pvBenefit": 1342.111, "storageBenefit": 369.621, "totalBenefit": 1711.732}, {"month": 11, "day": 17, "pvGeneration": 44.579, "storageCharge": 16.695, "storageDischarge": 15.528, "electricityConsumption": 43.5, "gridExport": 6.829, "gridImport": 8.146, "gridExportIncome": 102.435, "pvBenefit": 1234.521, "storageBenefit": 369.51, "totalBenefit": 1604.031}, {"month": 11, "day": 18, "pvGeneration": 49.319, "storageCharge": 16.694, "storageDischarge": 15.527, "electricityConsumption": 43.5, "gridExport": 11.5, "gridImport": 8.072, "gridExportIncome": 172.5, "pvBenefit": 1306.626, "storageBenefit": 369.84, "totalBenefit": 1676.467}, {"month": 11, "day": 19, "pvGeneration": 49.386, "storageCharge": 16.695, "storageDischarge": 15.527, "electricityConsumption": 43.5, "gridExport": 11.561, "gridImport": 8.072, "gridExportIncome": 173.415, "pvBenefit": 1307.806, "storageBenefit": 369.651, "totalBenefit": 1677.457}, {"month": 11, "day": 20, "pvGeneration": 32.647, "storageCharge": 12.061, "storageDischarge": 11.215, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 12.587, "gridExportIncome": 0, "pvBenefit": 1046.435, "storageBenefit": 295.92, "totalBenefit": 1342.355}, {"month": 11, "day": 21, "pvGeneration": 16.496, "storageCharge": 0.426, "storageDischarge": 0.396, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 27.064, "gridExportIncome": 0, "pvBenefit": 695.08, "storageBenefit": 11.113, "totalBenefit": 706.193}, {"month": 11, "day": 22, "pvGeneration": 19.531, "storageCharge": 6.041, "storageDischarge": 5.618, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 24.832, "gridExportIncome": 0, "pvBenefit": 681.133, "storageBenefit": 157.701, "totalBenefit": 838.833}, {"month": 11, "day": 23, "pvGeneration": 20.079, "storageCharge": 6.847, "storageDischarge": 6.368, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 24.4, "gridExportIncome": 0, "pvBenefit": 674.915, "storageBenefit": 178.761, "totalBenefit": 853.676}, {"month": 11, "day": 24, "pvGeneration": 29.07, "storageCharge": 8.998, "storageDischarge": 8.368, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 15.721, "gridExportIncome": 0, "pvBenefit": 983.392, "storageBenefit": 234.896, "totalBenefit": 1218.288}, {"month": 11, "day": 25, "pvGeneration": 22.138, "storageCharge": 7.676, "storageDischarge": 7.139, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 22.467, "gridExportIncome": 0, "pvBenefit": 728.527, "storageBenefit": 200.404, "totalBenefit": 928.931}, {"month": 11, "day": 26, "pvGeneration": 22.042, "storageCharge": 9.26, "storageDischarge": 8.61, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 22.786, "gridExportIncome": 0, "pvBenefit": 681.621, "storageBenefit": 241.662, "totalBenefit": 923.283}, {"month": 11, "day": 27, "pvGeneration": 15.277, "storageCharge": 0, "storageDischarge": 0, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 28.225, "gridExportIncome": 0, "pvBenefit": 652.942, "storageBenefit": 0, "totalBenefit": 652.942}, {"month": 11, "day": 28, "pvGeneration": 24.252, "storageCharge": 4.686, "storageDischarge": 4.357, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 19.921, "gridExportIncome": 0, "pvBenefit": 899.475, "storageBenefit": 122.289, "totalBenefit": 1021.764}, {"month": 11, "day": 29, "pvGeneration": 25.751, "storageCharge": 5.684, "storageDischarge": 5.284, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 18.567, "gridExportIncome": 0, "pvBenefit": 932.872, "storageBenefit": 148.293, "totalBenefit": 1081.165}, {"month": 11, "day": 30, "pvGeneration": 24.309, "storageCharge": 4.756, "storageDischarge": 4.421, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 19.874, "gridExportIncome": 0, "pvBenefit": 900.11, "storageBenefit": 124.068, "totalBenefit": 1024.179}, {"month": 12, "day": 1, "pvGeneration": 13.856, "storageCharge": 0, "storageDischarge": 0, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 29.642, "gridExportIncome": 0, "pvBenefit": 597.524, "storageBenefit": 0, "totalBenefit": 597.524}, {"month": 12, "day": 2, "pvGeneration": 31.662, "storageCharge": 11.565, "storageDischarge": 10.757, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 13.496, "gridExportIncome": 0, "pvBenefit": 1022.748, "storageBenefit": 288.109, "totalBenefit": 1310.856}, {"month": 12, "day": 3, "pvGeneration": 43.407, "storageCharge": 16.695, "storageDischarge": 15.527, "electricityConsumption": 43.5, "gridExport": 5.679, "gridImport": 8.169, "gridExportIncome": 85.185, "pvBenefit": 1216.015, "storageBenefit": 370, "totalBenefit": 1586.015}, {"month": 12, "day": 4, "pvGeneration": 16.838, "storageCharge": 5.359, "storageDischarge": 4.983, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 27.432, "gridExportIncome": 0, "pvBenefit": 566.473, "storageBenefit": 139.864, "totalBenefit": 706.336}, {"month": 12, "day": 5, "pvGeneration": 33.005, "storageCharge": 12.643, "storageDischarge": 11.757, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 12.312, "gridExportIncome": 0, "pvBenefit": 1047.724, "storageBenefit": 305.239, "totalBenefit": 1352.963}, {"month": 12, "day": 6, "pvGeneration": 47.232, "storageCharge": 16.695, "storageDischarge": 15.527, "electricityConsumption": 43.5, "gridExport": 9.49, "gridImport": 8.153, "gridExportIncome": 142.35, "pvBenefit": 1273.857, "storageBenefit": 369.825, "totalBenefit": 1643.682}, {"month": 12, "day": 7, "pvGeneration": 43.623, "storageCharge": 16.695, "storageDischarge": 15.527, "electricityConsumption": 43.5, "gridExport": 6.377, "gridImport": 8.65, "gridExportIncome": 95.655, "pvBenefit": 1210.453, "storageBenefit": 370, "totalBenefit": 1580.452}, {"month": 12, "day": 8, "pvGeneration": 20.793, "storageCharge": 1.613, "storageDischarge": 1.502, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 22.936, "gridExportIncome": 0, "pvBenefit": 839.414, "storageBenefit": 42.193, "totalBenefit": 881.608}, {"month": 12, "day": 9, "pvGeneration": 28.484, "storageCharge": 10.002, "storageDischarge": 9.302, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 16.451, "gridExportIncome": 0, "pvBenefit": 938.231, "storageBenefit": 261.118, "totalBenefit": 1199.349}, {"month": 12, "day": 10, "pvGeneration": 32.229, "storageCharge": 12.339, "storageDischarge": 11.475, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 13.041, "gridExportIncome": 0, "pvBenefit": 1027.813, "storageBenefit": 300.059, "totalBenefit": 1327.873}, {"month": 12, "day": 11, "pvGeneration": 38.298, "storageCharge": 16.694, "storageDischarge": 15.527, "electricityConsumption": 43.5, "gridExport": 0.895, "gridImport": 8.495, "gridExportIncome": 13.425, "pvBenefit": 1133.414, "storageBenefit": 370.015, "totalBenefit": 1503.429}, {"month": 12, "day": 12, "pvGeneration": 40.607, "storageCharge": 16.696, "storageDischarge": 15.527, "electricityConsumption": 43.5, "gridExport": 3.156, "gridImport": 8.446, "gridExportIncome": 47.34, "pvBenefit": 1168.946, "storageBenefit": 369.985, "totalBenefit": 1538.931}, {"month": 12, "day": 13, "pvGeneration": 39.754, "storageCharge": 16.695, "storageDischarge": 15.527, "electricityConsumption": 43.5, "gridExport": 2.411, "gridImport": 8.555, "gridExportIncome": 36.165, "pvBenefit": 1154.156, "storageBenefit": 370, "totalBenefit": 1524.156}, {"month": 12, "day": 14, "pvGeneration": 41.402, "storageCharge": 16.696, "storageDischarge": 15.527, "electricityConsumption": 43.5, "gridExport": 4.344, "gridImport": 8.837, "gridExportIncome": 65.16, "pvBenefit": 1174.083, "storageBenefit": 369.636, "totalBenefit": 1543.719}, {"month": 12, "day": 15, "pvGeneration": 41.592, "storageCharge": 16.695, "storageDischarge": 15.527, "electricityConsumption": 43.5, "gridExport": 4.227, "gridImport": 8.53, "gridExportIncome": 63.405, "pvBenefit": 1182.202, "storageBenefit": 370, "totalBenefit": 1552.202}, {"month": 12, "day": 16, "pvGeneration": 40.517, "storageCharge": 16.695, "storageDischarge": 15.527, "electricityConsumption": 43.5, "gridExport": 3.139, "gridImport": 8.517, "gridExportIncome": 47.085, "pvBenefit": 1166.297, "storageBenefit": 370, "totalBenefit": 1536.296}, {"month": 12, "day": 17, "pvGeneration": 43.528, "storageCharge": 16.694, "storageDischarge": 15.527, "electricityConsumption": 43.5, "gridExport": 5.991, "gridImport": 8.358, "gridExportIncome": 89.865, "pvBenefit": 1214.353, "storageBenefit": 370.015, "totalBenefit": 1584.368}, {"month": 12, "day": 18, "pvGeneration": 39.028, "storageCharge": 16.696, "storageDischarge": 15.527, "electricityConsumption": 43.5, "gridExport": 2.265, "gridImport": 9.137, "gridExportIncome": 33.975, "pvBenefit": 1132.808, "storageBenefit": 369.81, "totalBenefit": 1502.619}, {"month": 12, "day": 19, "pvGeneration": 34.296, "storageCharge": 14.046, "storageDischarge": 13.064, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 11.221, "gridExportIncome": 0, "pvBenefit": 1065.003, "storageBenefit": 327.717, "totalBenefit": 1392.72}, {"month": 12, "day": 20, "pvGeneration": 28.164, "storageCharge": 10.285, "storageDischarge": 9.565, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 16.81, "gridExportIncome": 0, "pvBenefit": 926.001, "storageBenefit": 267.615, "totalBenefit": 1193.616}, {"month": 12, "day": 21, "pvGeneration": 39.729, "storageCharge": 16.693, "storageDischarge": 15.527, "electricityConsumption": 43.5, "gridExport": 2.28, "gridImport": 8.446, "gridExportIncome": 34.2, "pvBenefit": 1155.95, "storageBenefit": 369.855, "totalBenefit": 1525.806}, {"month": 12, "day": 22, "pvGeneration": 27.043, "storageCharge": 7.512, "storageDischarge": 6.988, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 17.535, "gridExportIncome": 0, "pvBenefit": 943.05, "storageBenefit": 196.19, "totalBenefit": 1139.24}, {"month": 12, "day": 23, "pvGeneration": 17.759, "storageCharge": 7.393, "storageDischarge": 6.876, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 26.801, "gridExportIncome": 0, "pvBenefit": 564.342, "storageBenefit": 193.024, "totalBenefit": 757.366}, {"month": 12, "day": 24, "pvGeneration": 9.912, "storageCharge": 0, "storageDischarge": 0, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 33.588, "gridExportIncome": 0, "pvBenefit": 425.815, "storageBenefit": 0, "totalBenefit": 425.815}, {"month": 12, "day": 25, "pvGeneration": 19.799, "storageCharge": 7.273, "storageDischarge": 6.765, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 24.743, "gridExportIncome": 0, "pvBenefit": 652.738, "storageBenefit": 189.918, "totalBenefit": 842.656}, {"month": 12, "day": 26, "pvGeneration": 43.727, "storageCharge": 16.695, "storageDischarge": 15.527, "electricityConsumption": 43.5, "gridExport": 6.347, "gridImport": 8.514, "gridExportIncome": 95.205, "pvBenefit": 1214.832, "storageBenefit": 369.651, "totalBenefit": 1584.483}, {"month": 12, "day": 27, "pvGeneration": 42.437, "storageCharge": 16.697, "storageDischarge": 15.527, "electricityConsumption": 43.5, "gridExport": 5.158, "gridImport": 8.618, "gridExportIncome": 77.37, "pvBenefit": 1193.248, "storageBenefit": 369.97, "totalBenefit": 1563.218}, {"month": 12, "day": 28, "pvGeneration": 37.234, "storageCharge": 16.694, "storageDischarge": 15.527, "electricityConsumption": 43.5, "gridExport": 0.176, "gridImport": 8.84, "gridExportIncome": 2.64, "pvBenefit": 1111.141, "storageBenefit": 370.015, "totalBenefit": 1481.155}, {"month": 12, "day": 29, "pvGeneration": 38.802, "storageCharge": 16.696, "storageDischarge": 15.527, "electricityConsumption": 43.5, "gridExport": 2.044, "gridImport": 9.137, "gridExportIncome": 30.66, "pvBenefit": 1129.207, "storageBenefit": 369.985, "totalBenefit": 1499.192}, {"month": 12, "day": 30, "pvGeneration": 36.636, "storageCharge": 16.694, "storageDischarge": 15.527, "electricityConsumption": 43.5, "gridExport": 1.082, "gridImport": 10.345, "gridExportIncome": 16.23, "pvBenefit": 1076.101, "storageBenefit": 368.609, "totalBenefit": 1444.71}, {"month": 12, "day": 31, "pvGeneration": 25.139, "storageCharge": 7.947, "storageDischarge": 7.391, "electricityConsumption": 43.5, "gridExport": 0, "gridImport": 19.501, "gridExportIncome": 0, "pvBenefit": 837.043, "storageBenefit": 207.477, "totalBenefit": 1044.52}], "monthlyData": [{"month": 1, "pvGeneration": 1180.809, "storageCharge": 422.041, "storageDischarge": 392.525, "electricityConsumption": 1348.5, "gridExport": 164.588, "gridImport": 392.854, "gridExportIncome": 2468.82, "pvBenefit": 33951.698, "storageBenefit": 9609.696, "totalBenefit": 43561.394}, {"month": 2, "pvGeneration": 1350.42, "storageCharge": 434.08, "storageDischarge": 403.189, "electricityConsumption": 1218, "gridExport": 326.801, "gridImport": 257.174, "gridExportIncome": 4902.015, "pvBenefit": 36271.167, "storageBenefit": 9432.981, "totalBenefit": 45704.148}, {"month": 3, "pvGeneration": 1692.586, "storageCharge": 444.523, "storageDischarge": 412.938, "electricityConsumption": 1348.5, "gridExport": 568.003, "gridImport": 288.166, "gridExportIncome": 8520.045, "pvBenefit": 43640.895, "storageBenefit": 9752.978, "totalBenefit": 53393.873}, {"month": 4, "pvGeneration": 1959.716, "storageCharge": 453.541, "storageDischarge": 421.419, "electricityConsumption": 1305, "gridExport": 796.22, "gridImport": 206.965, "gridExportIncome": 11943.3, "pvBenefit": 48318.076, "storageBenefit": 9665.514, "totalBenefit": 57983.59}, {"month": 5, "pvGeneration": 2171.125, "storageCharge": 483.858, "storageDischarge": 449.935, "electricityConsumption": 1348.5, "gridExport": 911.208, "gridImport": 158.143, "gridExportIncome": 13668.12, "pvBenefit": 52969.992, "storageBenefit": 10198.84, "totalBenefit": 63168.832}, {"month": 6, "pvGeneration": 1828.637, "storageCharge": 447.944, "storageDischarge": 418.035, "electricityConsumption": 1305, "gridExport": 641.522, "gridImport": 180.87, "gridExportIncome": 9622.83, "pvBenefit": 46881.044, "storageBenefit": 9594.708, "totalBenefit": 56475.752}, {"month": 7, "pvGeneration": 1993.982, "storageCharge": 501.678, "storageDischarge": 465.431, "electricityConsumption": 1348.5, "gridExport": 713.793, "gridImport": 141.413, "gridExportIncome": 10706.895, "pvBenefit": 50355.581, "storageBenefit": 10499.735, "totalBenefit": 60855.317}, {"month": 8, "pvGeneration": 2026.28, "storageCharge": 501.316, "storageDischarge": 466.101, "electricityConsumption": 1348.5, "gridExport": 772.197, "gridImport": 166.53, "gridExportIncome": 11582.955, "pvBenefit": 50374.014, "storageBenefit": 10578.881, "totalBenefit": 60952.895}, {"month": 9, "pvGeneration": 1464.571, "storageCharge": 367.406, "storageDischarge": 342.69, "electricityConsumption": 1305, "gridExport": 426.788, "gridImport": 319.049, "gridExportIncome": 6401.82, "pvBenefit": 39832.941, "storageBenefit": 8008.318, "totalBenefit": 47841.26}, {"month": 10, "pvGeneration": 1271.796, "storageCharge": 360.585, "storageDischarge": 335.919, "electricityConsumption": 1348.5, "gridExport": 269.359, "gridImport": 397.292, "gridExportIncome": 4040.385, "pvBenefit": 36336.935, "storageBenefit": 8020.896, "totalBenefit": 44357.832}, {"month": 11, "pvGeneration": 1057.78, "storageCharge": 345.096, "storageDischarge": 320.938, "electricityConsumption": 1305, "gridExport": 144.747, "gridImport": 441.493, "gridExportIncome": 2171.205, "pvBenefit": 31269.261, "storageBenefit": 8011.713, "totalBenefit": 39280.974}, {"month": 12, "pvGeneration": 1036.532, "storageCharge": 391.792, "storageDischarge": 364.384, "electricityConsumption": 1348.5, "gridExport": 65.061, "gridImport": 433.256, "gridExportIncome": 975.915, "pvBenefit": 31360.982, "storageBenefit": 9005.891, "totalBenefit": 40366.873}], "yearlyData": {"pvGeneration": 19034.234, "storageCharge": 5153.86, "storageDischarge": 4793.504, "electricityConsumption": 15877.5, "gridExport": 5800.287, "gridImport": 3383.205, "gridExportIncome": 87004.305, "pvBenefit": 501562.586, "storageBenefit": 112380.152, "totalBenefit": 613942.739, "roi": 100.482, "paybackPeriod": 0.995}, "analysisCompleted": true, "analysisDate": "2025-05-30T15:25:31.232Z"}, "syncStatus": "synced", "dataType": "项目数据", "dataVersion": {"timestamp": "2025-05-30T15:25:31.236Z", "version": 2, "source": "frontend"}}