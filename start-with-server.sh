#!/bin/bash

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 默认管理员账号密码
DEFAULT_ADMIN_USERNAME="admin"
DEFAULT_ADMIN_PASSWORD="admin"
DEFAULT_SERVER_PORT=3001
DEFAULT_APP_PORT=5173

# 打印带颜色的信息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "光伏+储能项目经济性分析系统启动脚本（含服务器）"
    echo ""
    echo "用法: ./start-with-server.sh [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help                显示帮助信息"
    echo "  -u, --username USERNAME   设置管理员用户名 (默认: admin)"
    echo "  -p, --password PASSWORD   设置管理员密码 (默认: admin)"
    echo "  --app-port PORT           设置前端应用端口 (默认: 5173)"
    echo "  --server-port PORT        设置后端服务器端口 (默认: 3000)"
    echo ""
    echo "示例:"
    echo "  ./start-with-server.sh                使用默认设置启动应用"
    echo "  ./start-with-server.sh -u root -p 123456  使用自定义管理员账号启动应用"
    echo ""
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -u|--username)
                ADMIN_USERNAME="$2"
                shift 2
                ;;
            -p|--password)
                ADMIN_PASSWORD="$2"
                shift 2
                ;;
            --app-port)
                APP_PORT="$2"
                shift 2
                ;;
            --server-port)
                SERVER_PORT="$2"
                shift 2
                ;;
            *)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done

    # 设置默认值（如果未指定）
    ADMIN_USERNAME=${ADMIN_USERNAME:-$DEFAULT_ADMIN_USERNAME}
    ADMIN_PASSWORD=${ADMIN_PASSWORD:-$DEFAULT_ADMIN_PASSWORD}
    APP_PORT=${APP_PORT:-$DEFAULT_APP_PORT}
    SERVER_PORT=${SERVER_PORT:-$DEFAULT_SERVER_PORT}
}

# 创建或更新管理员账号
setup_admin_account() {
    print_info "设置管理员账号: $ADMIN_USERNAME"

    # 这里我们将管理员账号信息写入临时环境变量文件
    cat > .env.local << EOF
VITE_ADMIN_USERNAME=$ADMIN_USERNAME
VITE_ADMIN_PASSWORD=$ADMIN_PASSWORD
EOF

    # 确保环境变量文件存在于正确的位置
    if [ ! -f ".env.local" ]; then
        print_error "无法创建环境变量文件"
        exit 1
    fi

    print_success "管理员账号设置完成"
}

# 安装服务器依赖
install_server_dependencies() {
    print_info "检查服务器依赖..."

    # 检查服务器目录是否存在
    if [ ! -d "server" ]; then
        print_error "服务器目录不存在，请确保已创建server目录"
        exit 1
    fi

    # 进入服务器目录
    cd server

    # 检查node_modules目录是否存在
    if [ ! -d "node_modules" ]; then
        print_info "正在安装服务器依赖，请稍候..."
        npm install

        if [ $? -ne 0 ]; then
            print_error "服务器依赖安装失败，请检查错误信息"
            exit 1
        fi

        print_success "服务器依赖安装完成"
    else
        print_info "服务器依赖已安装，跳过安装步骤"
    fi

    # 返回项目根目录
    cd ..
}

# 启动服务器
start_server() {
    print_info "正在启动服务器，端口: $SERVER_PORT..."

    # 检查是否有正在运行的实例
    if command -v lsof >/dev/null 2>&1; then
        if lsof -i :$SERVER_PORT > /dev/null 2>&1; then
            print_warning "端口 $SERVER_PORT 已被占用，尝试停止现有实例..."
            kill $(lsof -t -i:$SERVER_PORT) 2>/dev/null || true
            sleep 2
        fi
    else
        print_warning "未找到lsof命令，无法检查端口占用情况"
    fi

    # 设置环境变量
    export PORT=$SERVER_PORT

    # 进入服务器目录
    cd server

    # 启动服务器
    npm run dev &
    SERVER_PID=$!

    # 保存进程ID
    echo $SERVER_PID > .server.pid

    # 返回项目根目录
    cd ..

    # 等待服务器启动
    sleep 3

    # 检查服务器是否成功启动
    if ps -p $SERVER_PID > /dev/null; then
        print_success "服务器已成功启动！"
        print_info "服务器地址: http://localhost:$SERVER_PORT"
    else
        print_error "服务器启动失败，请检查日志"
        exit 1
    fi
}

# 启动前端应用
start_app() {
    print_info "正在启动前端应用，端口: $APP_PORT..."

    # 检查是否有正在运行的实例
    if command -v lsof >/dev/null 2>&1; then
        if lsof -i :$APP_PORT > /dev/null 2>&1; then
            print_warning "端口 $APP_PORT 已被占用，尝试停止现有实例..."
            kill $(lsof -t -i:$APP_PORT) 2>/dev/null || true
            sleep 2
        fi
    else
        print_warning "未找到lsof命令，无法检查端口占用情况"
    fi

    # 设置环境变量
    export VITE_ADMIN_USERNAME=$ADMIN_USERNAME
    export VITE_ADMIN_PASSWORD=$ADMIN_PASSWORD

    # 启动应用
    npm run dev -- --port $APP_PORT &
    APP_PID=$!

    # 保存进程ID
    echo $APP_PID > .app.pid

    # 等待应用启动
    sleep 3

    # 检查应用是否成功启动
    if ps -p $APP_PID > /dev/null; then
        print_success "前端应用已成功启动！"
        print_info "访问地址: http://localhost:$APP_PORT"
    else
        print_error "前端应用启动失败，请检查日志"
        exit 1
    fi
}

# 修改API配置
update_api_config() {
    print_info "更新API配置..."

    # 检查API服务文件是否存在
    if [ ! -f "src/services/api.ts" ]; then
        print_error "API服务文件不存在: src/services/api.ts"
        return 1
    fi

    # 更新API基础URL
    sed -i '' "s|baseURL: 'http://localhost:[0-9]*/api'|baseURL: 'http://localhost:$SERVER_PORT/api'|g" src/services/api.ts

    print_success "API配置已更新"
}

# 主函数
main() {
    # 解析命令行参数
    parse_args "$@"

    # 显示欢迎信息
    echo "=================================================="
    echo "  光伏+储能项目经济性分析系统启动脚本（含服务器）"
    echo "=================================================="
    echo ""

    # 设置管理员账号
    setup_admin_account

    # 安装服务器依赖
    install_server_dependencies

    # 更新API配置
    update_api_config

    # 启动服务器
    start_server

    # 启动前端应用
    start_app

    echo ""
    echo "=================================================="
    echo "  系统已成功启动"
    echo "  按 Ctrl+C 停止系统"
    echo "=================================================="
    echo "  前端应用地址: http://localhost:$APP_PORT"
    echo "  后端服务器地址: http://localhost:$SERVER_PORT"
    echo "  超级管理员账号: $ADMIN_USERNAME"
    echo "  超级管理员密码: $ADMIN_PASSWORD"
    echo "=================================================="

    # 等待用户按Ctrl+C
    wait
}

# 执行主函数
main "$@"
